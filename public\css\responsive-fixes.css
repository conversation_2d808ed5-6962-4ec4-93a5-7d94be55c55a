/**
 * Responsive Fixes for Brandify E-commerce Platform
 * Applied across the entire application for improved mobile experience
 */

/* Global mobile fixes */
body {
    overflow-x: hidden;
}

/* Responsive breakpoint adjustments */
.container, .container-fluid {
    padding-left: var(--bs-gutter-x, 0.75rem);
    padding-right: var(--bs-gutter-x, 0.75rem);
}

/* Chart and map containers */
.chart-container {
    position: relative;
    height: 300px;
    max-height: 300px;
    width: 100%;
    overflow: hidden;
}

.chart-container.small {
    height: 230px;
    max-height: 230px;
}

#nigeria-map-container {
    position: relative;
    height: 380px;
    max-height: 380px;
    overflow: hidden;
    border-bottom: 1px solid #dee2e6;
}

/* Dashboard card styling */
.card {
    transition: all 0.2s ease;
    border-radius: 0.5rem;
    overflow: hidden;
    height: 100%;
}

.card:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Stats icon styling */
.stat-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: var(--bs-light, #f8f9fa);
}

/* Mobile navbar improvements */
@media (max-width: 991.98px) {
    .navbar-collapse {
        background: white;
        padding: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-top: 0.5rem;
        z-index: 1050;
    }
    
    .navbar-nav {
        margin-bottom: 1rem;
    }
    
    form.d-flex {
        margin: 1rem 0;
        width: 100%;
    }
    
    .d-flex.align-items-center.gap-4 {
        display: flex;
        justify-content: space-around;
        width: 100%;
        margin-top: 0.5rem;
    }
    
    /* Vendor sidebar */
    .sidebar {
        margin-left: -280px;
        box-shadow: 5px 0 10px rgba(0,0,0,0.1);
        z-index: 1060;
        position: relative;
    }
    
    .sidebar.show {
        margin-left: 0;
    }
    
    .content {
        margin-left: 0;
        padding: 1.5rem 1rem;
    }
    
    .sidebar-toggle {
        display: block;
    }
    
    .sidebar-overlay.show {
        display: block;
    }
    
    /* Chart and map containers on medium screens */
    .chart-container, 
    .chart-container.small {
        height: 250px;
    }
    
    #nigeria-map-container {
        height: 300px;
    }
    
    .card:hover {
        transform: none;
    }
}

/* Small mobile screens */
@media (max-width: 767.98px) {
    .container.pt-5.mt-5 {
        padding-top: 4.5rem !important;
        margin-top: 3.5rem !important;
    }
    
    .chart-container, 
    .chart-container.small {
        height: 200px;
    }
    
    #nigeria-map-container {
        height: 250px;
    }
    
    .container-fluid {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    .row > [class*="col-"] {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
    }
    
    h3.fw-bold {
        font-size: 1.4rem;
    }
}

/* Extra small devices */
@media (max-width: 575.98px) {
    .table th, .table td {
        padding: 0.5rem;
        font-size: 0.875rem;
    }
    
    .card-header {
        padding: 0.75rem;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .dropdown-menu {
        font-size: 0.875rem;
    }
}

/* Specific fixes for vendor dashboard */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table th, .table td {
    white-space: nowrap;
}

/* Ensure all product cards are consistent height */
.product-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card .card-body {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
}

.product-card .card-footer {
    margin-top: auto;
}

/* Fix mobile product grid */
@media (max-width: 767.98px) {
    .product-grid .col-md-4, 
    .product-grid .col-lg-3 {
        width: 50%;
    }
}

@media (max-width: 575.98px) {
    .product-grid .col-md-4, 
    .product-grid .col-lg-3 {
        width: 100%;
    }
}
