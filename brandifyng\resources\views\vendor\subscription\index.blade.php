@extends('layouts.vendor')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0 fw-bold">Subscription Management</h2>
    </div>
    
    @if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif
    
    @if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif
    
    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-dark text-white py-3">
                    <h5 class="mb-0">Current Subscription</h5>
                </div>
                <div class="card-body">
                    @if($activeSubscription)
                        <div class="d-flex align-items-center mb-3">
                            <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                <i class="fas fa-crown text-warning"></i>
                            </div>
                            <div>
                                <h4 class="mb-0">{{ $activeSubscription->plan->name ?? 'Free Plan' }}</h4>
                                <p class="text-muted mb-0">{{ $activeSubscription->plan->description ?? 'Basic vendor features' }}</p>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="d-flex justify-content-between mb-1">
                                <span>Subscription Status</span>
                                <span class="badge bg-success">Active</span>
                            </div>
                            <div class="d-flex justify-content-between mb-1">
                                <span>Start Date</span>
                                <span>{{ $activeSubscription->start_date->format('M d, Y') }}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-1">
                                <span>Expiration Date</span>
                                <span>{{ $activeSubscription->end_date->format('M d, Y') }}</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Time Remaining</span>
                                <span>{{ now()->diffInDays($activeSubscription->end_date, false) }} days</span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="progress" style="height: 10px;">
                                @php
                                    $totalDays = $activeSubscription->start_date->diffInDays($activeSubscription->end_date);
                                    $remainingDays = now()->diffInDays($activeSubscription->end_date, false);
                                    $progressPercentage = $totalDays > 0 ? 100 - (($remainingDays / $totalDays) * 100) : 0;
                                @endphp
                                <div class="progress-bar bg-success" role="progressbar" style="width: {{ $progressPercentage }}%;" aria-valuenow="{{ $progressPercentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <div class="d-flex justify-content-between mt-1">
                                <small>{{ $activeSubscription->start_date->format('M d') }}</small>
                                <small>{{ $activeSubscription->end_date->format('M d') }}</small>
                            </div>
                        </div>
                        
                        @if($remainingDays <= 7)
                            <div class="alert alert-warning mb-3" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Your subscription expires in {{ $remainingDays }} {{ Str::plural('day', $remainingDays) }}. Please renew to avoid service interruption.
                            </div>
                        @endif
                        
                        <form action="{{ route('vendor.subscription.cancel') }}" method="POST" onsubmit="return confirm('Are you sure you want to cancel your subscription? This action cannot be undone.');">
                            @csrf
                            <button type="submit" class="btn btn-outline-danger">Cancel Subscription</button>
                        </form>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-crown fa-3x text-muted mb-3"></i>
                            <h5>No Active Subscription</h5>
                            <p class="text-muted mb-4">You are currently on the free plan with limited features.</p>
                            <p>Choose a plan below to upgrade your account and access more features.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <h4 class="mb-3">Available Plans</h4>
            <div class="row">
                @foreach($plans as $plan)
                    <div class="col-md-6 mb-4">
                        <div class="card h-100 {{ $activeSubscription && $activeSubscription->subscription_plan_id == $plan->id ? 'border-success' : '' }}">
                            <div class="card-header bg-light py-3">
                                <h5 class="mb-0">{{ $plan->name }}</h5>
                                @if($activeSubscription && $activeSubscription->subscription_plan_id == $plan->id)
                                    <span class="badge bg-success">Current Plan</span>
                                @endif
                            </div>
                            <div class="card-body">
                                <h3 class="card-title text-center mb-3">${{ number_format($plan->price, 2) }}<small class="text-muted">/{{ $plan->duration }} days</small></h3>
                                <p class="text-muted mb-4">{{ $plan->description }}</p>
                                
                                <ul class="list-unstyled mb-4">
                                    @foreach(explode(',', $plan->features) as $feature)
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i> {{ trim($feature) }}</li>
                                    @endforeach
                                </ul>
                                
                                @if(!$activeSubscription || $activeSubscription->subscription_plan_id != $plan->id)
                                    <a href="{{ route('vendor.subscription.create', $plan->id) }}" class="btn btn-dark w-100">
                                        {{ $activeSubscription ? 'Change Plan' : 'Subscribe' }}
                                    </a>
                                @else
                                    <button class="btn btn-success w-100" disabled>Current Plan</button>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Subscription History</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th>Plan</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($subscriptionHistory as $subscription)
                                    <tr>
                                        <td>{{ $subscription->plan->name }}</td>
                                        <td>{{ $subscription->start_date->format('M d, Y') }}</td>
                                        <td>{{ $subscription->end_date->format('M d, Y') }}</td>
                                        <td>${{ number_format($subscription->plan->price, 2) }}</td>
                                        <td>
                                            @if($subscription->is_active && now()->lt($subscription->end_date))
                                                <span class="badge bg-success">Active</span>
                                            @elseif($subscription->is_active && now()->gt($subscription->end_date))
                                                <span class="badge bg-danger">Expired</span>
                                            @else
                                                <span class="badge bg-secondary">Cancelled</span>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="text-center py-4">No subscription history available</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
