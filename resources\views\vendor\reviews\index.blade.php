@extends('layouts.vendor')

@section('title', 'Reviews Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="fw-bold">Reviews Management</h2>
                <div class="d-flex gap-2">
                    <a href="{{ route('vendor.reviews.index') }}" class="btn btn-outline-dark {{ !request('status') ? 'active' : '' }}">All</a>
                    <a href="{{ route('vendor.reviews.index', ['status' => 'pending']) }}" class="btn btn-outline-warning {{ request('status') == 'pending' ? 'active' : '' }}">Pending</a>
                    <a href="{{ route('vendor.reviews.index', ['status' => 'approved']) }}" class="btn btn-outline-success {{ request('status') == 'approved' ? 'active' : '' }}">Approved</a>
                    <a href="{{ route('vendor.reviews.index', ['status' => 'not_responded']) }}" class="btn btn-outline-info {{ request('status') == 'not_responded' ? 'active' : '' }}">Need Response</a>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="card">
                <div class="card-body">
                    @if($reviews->count() > 0)
                        @foreach($reviews as $review)
                            <div class="card mb-3 {{ !$review->is_approved ? 'border-warning' : '' }}">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="d-flex justify-content-between mb-2">
                                                <h6 class="fw-bold mb-0">{{ $review->user->name }}</h6>
                                                <div>
                                                    @if(!$review->is_approved)
                                                        <span class="badge bg-warning">Pending Approval</span>
                                                    @else
                                                        <span class="badge bg-success">Approved</span>
                                                    @endif
                                                    @if($review->vendor_response)
                                                        <span class="badge bg-info">Responded</span>
                                                    @endif
                                                </div>
                                            </div>
                                            
                                            <div class="mb-2">
                                                @for($i = 1; $i <= 5; $i++)
                                                    @if($i <= $review->rating)
                                                        <i class="fas fa-star text-warning"></i>
                                                    @else
                                                        <i class="far fa-star text-warning"></i>
                                                    @endif
                                                @endfor
                                                <span class="ms-2 text-muted">{{ $review->created_at->format('M d, Y') }}</span>
                                            </div>
                                            
                                            <p class="mb-2"><strong>Product:</strong> {{ $review->product->name }}</p>
                                            <p class="mb-0">{{ $review->comment }}</p>
                                            
                                            @if($review->vendor_response)
                                                <div class="mt-3 p-3 bg-light rounded">
                                                    <h6 class="fw-bold mb-2 text-primary">Your Response:</h6>
                                                    <p class="mb-0">{{ $review->vendor_response }}</p>
                                                    <small class="text-muted">Responded on {{ $review->vendor_response_date->format('M d, Y') }}</small>
                                                </div>
                                            @endif
                                        </div>
                                        
                                        <div class="col-md-4 text-end">
                                            @if($review->is_approved)
                                                @if(!$review->vendor_response)
                                                    <a href="{{ route('vendor.reviews.show', $review) }}" class="btn btn-primary btn-sm">
                                                        <i class="fas fa-reply me-1"></i>Respond
                                                    </a>
                                                @else
                                                    <a href="{{ route('vendor.reviews.show', $review) }}" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-edit me-1"></i>Edit Response
                                                    </a>
                                                @endif
                                            @else
                                                <span class="text-muted small">Waiting for admin approval</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $reviews->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-star fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No reviews found</h5>
                            <p class="text-muted">
                                @if(request('status') == 'pending')
                                    No pending reviews at the moment.
                                @elseif(request('status') == 'not_responded')
                                    All reviews have been responded to.
                                @else
                                    Your products haven't received any reviews yet.
                                @endif
                            </p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
