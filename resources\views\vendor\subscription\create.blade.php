@extends('layouts.vendor')

@section('content')
<div class="container-fluid">
    <div class="mb-4">
        <a href="{{ route('vendor.subscription.index') }}" class="text-decoration-none text-dark">
            <i class="fas fa-arrow-left me-2"></i> Back to Subscription Management
        </a>
    </div>
    
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Subscribe to {{ $plan->name }}</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-4">
                                <h6 class="text-uppercase text-muted mb-2">Plan Summary</h6>
                                <p class="mb-1"><strong>Plan:</strong> {{ $plan->name }}</p>
                                <p class="mb-1"><strong>Price:</strong> ₦{{ number_format($plan->price, 2) }}</p>
                                <p class="mb-1"><strong>Duration:</strong> {{ $plan->duration }} days</p>
                                <p><strong>Renewal Date:</strong> {{ now()->addDays($plan->duration)->format('M d, Y') }}</p>
                            </div>
                            
                            <div class="mb-4">
                                <h6 class="text-uppercase text-muted mb-2">Plan Features</h6>
                                <ul class="list-unstyled">
                                    @foreach(explode(',', $plan->features) as $feature)
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i> {{ trim($feature) }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">Payment Summary</h5>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Subscription Plan</span>
                                        <span>₦{{ number_format($plan->price, 2) }}</span>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between mb-2 fw-bold">
                                        <span>Total Payment</span>
                                        <span>₦{{ number_format($plan->price, 2) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <form id="payment-form" action="{{ route('vendor.subscription.store', $plan->id) }}" method="POST">
                        @csrf
                        <div class="mb-4">
                            <h6 class="text-uppercase text-muted mb-3">Payment Method</h6>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="radio" name="payment_method" id="paystack" value="paystack" checked>
                                <label class="form-check-label" for="paystack">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-credit-card me-2"></i> Pay with Card (Paystack)
                                    </div>
                                </label>
                            </div>
                        </div>
                        
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            By clicking "Complete Subscription", you will be redirected to Paystack secure checkout to complete your payment.
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button id="complete-button" type="submit" class="btn btn-dark">
                                Complete Subscription
                            </button>
                            <a href="{{ route('vendor.subscription.index') }}" class="btn btn-outline-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('payment-form');
        const completeButton = document.getElementById('complete-button');
        
        form.addEventListener('submit', function(e) {
            // Disable the button to prevent multiple submissions
            completeButton.disabled = true;
            completeButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> Processing...';
        });
    });
</script>
@endsection
