<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Vendor;
use App\Models\User;
use App\Models\Role;

class SubscriptionTestSeeder extends Seeder
{
    /**
     * Run the database seeders.
     */
    public function run(): void
    {
        // Update existing vendors with subscription data
        $vendors = Vendor::all();
        
        foreach ($vendors as $index => $vendor) {
            // Simulate different subscription scenarios
            switch ($index % 4) {
                case 0:
                    // Vendor with active subscription
                    $vendor->update([
                        'subscription_status' => 'active',
                        'orders_processed' => rand(15, 50),
                        'free_order_limit' => 10,
                        'subscription_started_at' => now()->subDays(rand(10, 60)),
                        'subscription_expires_at' => now()->addDays(rand(10, 90)),
                        'monthly_subscription_fee' => 50.00,
                        'subscription_required' => false,
                    ]);
                    break;
                    
                case 1:
                    // Vendor approaching free limit
                    $vendor->update([
                        'subscription_status' => 'inactive',
                        'orders_processed' => 8,
                        'free_order_limit' => 10,
                        'subscription_started_at' => null,
                        'subscription_expires_at' => null,
                        'monthly_subscription_fee' => 50.00,
                        'subscription_required' => false,
                    ]);
                    break;
                    
                case 2:
                    // Vendor who needs subscription
                    $vendor->update([
                        'subscription_status' => 'inactive',
                        'orders_processed' => 12,
                        'free_order_limit' => 10,
                        'subscription_started_at' => null,
                        'subscription_expires_at' => null,
                        'monthly_subscription_fee' => 50.00,
                        'subscription_required' => true,
                    ]);
                    break;
                    
                case 3:
                    // Vendor with expired subscription
                    $vendor->update([
                        'subscription_status' => 'cancelled',
                        'orders_processed' => rand(20, 100),
                        'free_order_limit' => 10,
                        'subscription_started_at' => now()->subDays(rand(90, 180)),
                        'subscription_expires_at' => now()->subDays(rand(1, 30)),
                        'monthly_subscription_fee' => 50.00,
                        'subscription_required' => true,
                    ]);
                    break;
            }
        }
        
        $this->command->info('Updated ' . $vendors->count() . ' vendors with subscription test data.');
    }
}
