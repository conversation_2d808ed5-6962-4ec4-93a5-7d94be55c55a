<?php

namespace App\Http\Middleware;

use Closure;
use App\Models\Product;
use Illuminate\Http\Request;
use App\Services\ViewedProductsService;
use Symfony\Component\HttpFoundation\Response;

class TrackProductViews
{
    /**
     * The viewed products service.
     *
     * @var \App\Services\ViewedProductsService
     */
    protected $viewedProductsService;

    /**
     * Create a new middleware instance.
     *
     * @param  \App\Services\ViewedProductsService  $viewedProductsService
     * @return void
     */
    public function __construct(ViewedProductsService $viewedProductsService)
    {
        $this->viewedProductsService = $viewedProductsService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only track GET requests for product pages
        if ($request->isMethod('GET') && $request->route() && $request->route()->getName() === 'products.show') {
            $product = $request->route('product');
            
            if ($product instanceof Product && $product->exists) {
                $this->viewedProductsService->addProduct($product);
            }
        }

        return $response;
    }
}
