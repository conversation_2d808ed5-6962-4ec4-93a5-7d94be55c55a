@extends('layouts.app')

@section('content')
    <div class="container py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="fw-bold mb-4">My Account</h1>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="rounded-circle bg-dark text-white d-flex align-items-center justify-content-center me-3"
                                style="width: 48px; height: 48px;">
                                {{ auth()->user()->initials() }}
                            </div>
                            <div>
                                <h5 class="fw-bold mb-0">{{ auth()->user()->name }}</h5>
                                <p class="text-muted mb-0">{{ auth()->user()->email }}</p>
                            </div>
                        </div>

                        <div class="list-group list-group-flush">
                            <a href="{{ route('dashboard') }}" class="list-group-item list-group-item-action active">
                                <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                            </a>
                            <a href="{{ route('orders.index') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-shopping-bag me-2"></i> My Orders
                            </a>
                            <a href="{{ route('wishlist.index') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-heart me-2"></i> My Wishlist
                            </a>
                            <a href="{{ route('settings.profile') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-user-cog me-2"></i> Account Settings
                            </a>
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" class="list-group-item list-group-item-action text-danger">
                                    <i class="fas fa-sign-out-alt me-2"></i> Logout
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="fw-bold mb-0">Recent Orders</h5>
                                    <a href="{{ route('orders.index') }}" class="text-decoration-none">View All</a>
                                </div>

                                @if (count(auth()->user()->orders) > 0)
                                    <div class="list-group list-group-flush">
                                        @foreach (auth()->user()->orders->take(3) as $order)
                                            <a href="{{ route('orders.show', $order) }}"
                                                class="list-group-item list-group-item-action">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <p class="fw-bold mb-0">Order
                                                            #{{ $order->order_number ?? 'ORD-' . $order->id }}</p>
                                                        <p class="text-muted mb-0">
                                                            {{ $order->created_at->format('M d, Y') }}</p>
                                                    </div>
                                                    <div class="text-end">
                                                        <span
                                                            class="badge bg-{{ $order->status === 'completed' ? 'success' : ($order->status === 'pending' ? 'warning' : 'info') }}">
                                                            {{ ucfirst($order->status) }}
                                                        </span>
                                                        <p class="text-muted mb-0 small">
                                                            ₦{{ number_format($order->total_amount ?? 0, 2) }}</p>
                                                        @if ($order->shipbubbles_tracking_id)
                                                            <p class="text-muted mb-0 small">Track:
                                                                {{ $order->shipbubbles_tracking_id }}</p>
                                                        @endif
                                                    </div>
                                                </div>
                                            </a>
                                        @endforeach
                                    </div>
                                @else
                                    <div class="text-center py-4">
                                        <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                                        <p class="mb-0">You haven't placed any orders yet.</p>
                                        <a href="{{ route('products.index') }}" class="btn btn-dark mt-3">Start
                                            Shopping</a>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="fw-bold mb-0">Wishlist</h5>
                                    <a href="{{ route('wishlist.index') }}" class="text-decoration-none">View All</a>
                                </div>

                                @php
                                    $wishlistProducts = [];
                                    foreach (auth()->user()->wishlist as $wishlist) {
                                        foreach ($wishlist->items as $item) {
                                            if ($item->product) {
                                                $wishlistProducts[] = $item->product;
                                            }
                                        }
                                    }
                                    $wishlistProducts = collect($wishlistProducts)->take(3);
                                @endphp

                                @if (count($wishlistProducts) > 0)
                                    <div class="list-group list-group-flush">
                                        @foreach ($wishlistProducts as $product)
                                            <div class="list-group-item">
                                                <div class="d-flex align-items-center">
                                                    <img src="{{ $product->image_url ?? 'https://via.placeholder.com/50' }}"
                                                        alt="{{ $product->name }}" class="img-fluid rounded me-3"
                                                        style="width: 50px; height: 50px; object-fit: cover;">
                                                    <div>
                                                        <p class="fw-bold mb-0">{{ $product->name }}</p>
                                                        <p class="text-muted mb-0">₦{{ number_format($product->price, 2) }}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <div class="text-center py-4">
                                        <i class="fas fa-heart fa-3x text-muted mb-3"></i>
                                        <p class="mb-0">Your wishlist is empty.</p>
                                        <a href="{{ route('products.index') }}" class="btn btn-dark mt-3">Explore
                                            Products</a>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="fw-bold mb-4">Account Information</h5>

                                <div class="row">
                                    <div class="col-md-6 mb-4">
                                        <h6 class="fw-bold">Personal Information</h6>
                                        <p class="mb-1"><strong>Name:</strong> {{ auth()->user()->name }}</p>
                                        <p class="mb-1"><strong>Email:</strong> {{ auth()->user()->email }}</p>
                                        <p class="mb-3"><strong>Joined:</strong>
                                            {{ auth()->user()->created_at->format('M d, Y') }}</p>

                                        <a href="{{ route('settings.profile') }}" class="btn btn-sm btn-outline-dark">Edit
                                            Profile</a>
                                    </div>

                                    <div class="col-md-6 mb-4">
                                        <h6 class="fw-bold">Default Shipping Address</h6>
                                        @if (isset(auth()->user()->address))
                                            <p class="mb-1">{{ auth()->user()->address->address }}</p>
                                            <p class="mb-1">{{ auth()->user()->address->city }},
                                                {{ auth()->user()->address->state }}
                                                {{ auth()->user()->address->postal_code }}</p>
                                            <p class="mb-3">{{ auth()->user()->address->country }}</p>

                                            <a href="{{ route('settings.profile') }}"
                                                class="btn btn-sm btn-outline-dark">Edit Address</a>
                                        @else
                                            <p class="text-muted mb-3">No default shipping address set.</p>
                                            <a href="{{ route('settings.profile') }}"
                                                class="btn btn-sm btn-outline-dark">Add Address</a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
