<?php

namespace Database\Seeders;


use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Vendor;
use App\Models\Category;
use Illuminate\Support\Str;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all vendors (which are now also the brands)
        $vendors = Vendor::all();

        // Create default categories if none exist
        $categories = [
            ['name' => 'Clothing', 'slug' => 'clothing', 'description' => 'Clothing and apparel'],
            ['name' => 'Electronics', 'slug' => 'electronics', 'description' => 'Electronic devices and gadgets'],
            ['name' => 'Accessories', 'slug' => 'accessories', 'description' => 'Fashion and tech accessories'],
            ['name' => 'Footwear', 'slug' => 'footwear', 'description' => 'Shoes and footwear'],
        ];

        $categoryModels = [];
        foreach ($categories as $categoryData) {
            $categoryModels[] = Category::firstOrCreate(
                ['slug' => $categoryData['slug']],
                [
                    'name' => $categoryData['name'],
                    'description' => $categoryData['description'],
                    'is_active' => true,
                    'order' => count($categoryModels) + 1
                ]
            );
        }

        // Create products for each vendor
        foreach ($vendors as $vendor) {
            // Create 10 products for each vendor
            for ($i = 1; $i <= 10; $i++) {
                $name = $vendor->shop_name . ' Product ' . $i;
                $isFeatured = $i <= 3; // First 3 products are featured
                $price = rand(1000, 50000) / 100; // Random price between $10 and $500
                $hasDiscount = rand(0, 1); // 50% chance of discount
                $discountPrice = $hasDiscount ? rand(500, $price * 100 - 100) / 100 : null;

                Product::updateOrCreate(
                    ['slug' => Str::slug($name)],
                    [
                        'name' => $name,
                        'description' => 'This is a sample product from ' . $vendor->shop_name,
                        'price' => $price,
                        'discount_price' => $discountPrice,
                        'stock_quantity' => rand(0, 100),
                        'sku' => strtoupper(Str::random(8)),
                        'is_active' => true,
                        'is_featured' => $isFeatured,
                        'vendor_id' => $vendor->id,
                        'category_id' => $categoryModels[array_rand($categoryModels)]->id,
                        'requires_platform_delivery' => rand(0, 1),
                        'weight' => rand(100, 5000) / 100, // Random weight between 1kg and 50kg
                        'length' => rand(10, 100), // Random length between 10cm and 100cm
                        'width' => rand(10, 100), // Random width between 10cm and 100cm
                        'height' => rand(5, 50), // Random height between 5cm and 50cm
                        'created_at' => now()->subDays(rand(1, 30)) // Random creation date in the last 30 days
                    ]
                );
            }
        }
    }
}
