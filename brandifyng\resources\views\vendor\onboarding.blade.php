@extends('layouts.app')

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-5">
                <h1 class="fw-bold mb-2">Complete Your Vendor Profile</h1>
                <p class="text-muted">Provide your payment details to start receiving payments for your sales.</p>
            </div>
            
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <ul class="nav nav-pills card-header-pills">
                        <li class="nav-item">
                            <a class="nav-link disabled" href="#registration">
                                <i class="fas fa-check-circle text-success me-2"></i> Registration
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ $vendor->is_approved ? 'disabled' : 'active' }}" href="#verification">
                                @if($vendor->is_approved)
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                @else
                                    <i class="fas fa-spinner fa-spin me-2"></i>
                                @endif
                                Verification
                                @if(!$vendor->is_approved)
                                    <span class="badge bg-warning text-dark ms-1">Pending</span>
                                @endif
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ $vendor->is_approved ? 'active' : 'disabled' }}" href="#setup">
                                <i class="fas fa-store me-2"></i> Store Setup
                            </a>
                        </li>
                    </ul>
                </div>
                
                <div class="card-body p-4">
                    <div class="tab-content">
                        @if(!$vendor->is_approved)
                            <div class="tab-pane fade show active" id="verification">
                                <div class="text-center py-5">
                                    <div class="mb-4">
                                        <i class="fas fa-hourglass-half fa-4x text-warning"></i>
                                    </div>
                                    <h4 class="fw-bold mb-3">Your application is under review</h4>
                                    <p class="text-muted mb-4">
                                        Thank you for submitting your application to become a vendor. Our team is currently reviewing your information and documents.
                                        This process typically takes 1-2 business days.
                                    </p>
                                    <div class="alert alert-light">
                                        <p class="mb-0">We'll notify you via email at <strong>{{ auth()->user()->email }}</strong> once your application has been processed.</p>
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="tab-pane fade show active" id="setup">
                                @if(!$vendor->has_completed_onboarding)
                                    <form method="POST" action="{{ route('vendor.complete-profile') }}">
                                        @csrf
                                        
                                        <div class="row mb-4">
                                            <div class="col-12">
                                                <h5 class="fw-bold mb-3">Payment Information</h5>
                                                <p class="text-muted small mb-3">This information will be used to send your payments. Please ensure all details are accurate.</p>
                                            </div>
                                            
                                            <div class="col-md-6 mb-3">
                                                <label for="bank_name" class="form-label">Bank Name <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control @error('bank_name') is-invalid @enderror" id="bank_name" name="bank_name" value="{{ old('bank_name', $vendor->bank_name) }}" required>
                                                @error('bank_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                            
                                            <div class="col-md-6 mb-3">
                                                <label for="bank_account_name" class="form-label">Account Holder Name <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control @error('bank_account_name') is-invalid @enderror" id="bank_account_name" name="bank_account_name" value="{{ old('bank_account_name', $vendor->bank_account_name) }}" required>
                                                @error('bank_account_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                            
                                            <div class="col-md-6 mb-3">
                                                <label for="bank_account_number" class="form-label">Account Number <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control @error('bank_account_number') is-invalid @enderror" id="bank_account_number" name="bank_account_number" value="{{ old('bank_account_number', $vendor->bank_account_number) }}" required>
                                                @error('bank_account_number')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                            
                                            <div class="col-md-6 mb-3">
                                                <label for="tax_id" class="form-label">Tax ID / VAT Number</label>
                                                <input type="text" class="form-control @error('tax_id') is-invalid @enderror" id="tax_id" name="tax_id" value="{{ old('tax_id', $vendor->tax_id) }}">
                                                @error('tax_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                            
                                            <div class="col-md-6 mb-3">
                                                <label for="paypal_email" class="form-label">PayPal Email (Alternative)</label>
                                                <input type="email" class="form-control @error('paypal_email') is-invalid @enderror" id="paypal_email" name="paypal_email" value="{{ old('paypal_email', $vendor->paypal_email) }}">
                                                @error('paypal_email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <small class="text-muted">Optional: If you prefer to receive payments via PayPal</small>
                                            </div>
                                        </div>
                                        
                                        <div class="row mb-4">
                                            <div class="col-12">
                                                <h5 class="fw-bold mb-3">Commission Agreement</h5>
                                                <div class="alert alert-light">
                                                    <p class="mb-3">By selling on our marketplace, you agree to our commission structure:</p>
                                                    <ul class="mb-0">
                                                        <li>Standard commission rate: <strong>10%</strong> of the product's selling price</li>
                                                        <li>Promotional items commission: <strong>12%</strong> of the product's selling price</li>
                                                        <li>Payments are processed within <strong>7 days</strong> after order completion</li>
                                                        <li>Minimum payout threshold: <strong>$50</strong></li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="form-check mb-4">
                                            <input class="form-check-input @error('commission_agreement') is-invalid @enderror" type="checkbox" id="commission_agreement" name="commission_agreement" required>
                                            <label class="form-check-label" for="commission_agreement">
                                                I understand and agree to the commission structure and payment terms
                                            </label>
                                            @error('commission_agreement')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-dark btn-lg">
                                                Complete Setup & Start Selling
                                            </button>
                                        </div>
                                    </form>
                                @else
                                    <div class="text-center py-5">
                                        <div class="mb-4">
                                            <i class="fas fa-check-circle fa-4x text-success"></i>
                                        </div>
                                        <h4 class="fw-bold mb-3">Your store setup is complete!</h4>
                                        <p class="text-muted mb-4">
                                            Congratulations! Your vendor account is fully set up and ready to go.
                                            You can now start adding products and managing your store.
                                        </p>
                                        <div class="row justify-content-center">
                                            <div class="col-md-6">
                                                <a href="{{ route('vendor.dashboard') }}" class="btn btn-dark btn-lg w-100">
                                                    <i class="fas fa-tachometer-alt me-2"></i> Go to Dashboard
                                                </a>
                                            </div>
                                            <div class="col-md-6 mt-3 mt-md-0">
                                                <a href="{{ route('vendor.products.create') }}" class="btn btn-outline-dark btn-lg w-100">
                                                    <i class="fas fa-plus me-2"></i> Add Your First Product
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            </div>
            
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-body p-4">
                    <h5 class="fw-bold mb-3">Getting Started Guide</h5>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="d-flex mb-3">
                                <div class="flex-shrink-0">
                                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 36px; height: 36px;">
                                        <span class="fw-bold">1</span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="fw-bold mb-1">Add Your Products</h6>
                                    <p class="small text-muted mb-0">Upload images and details for all your products</p>
                                </div>
                            </div>
                            
                            <div class="d-flex mb-3">
                                <div class="flex-shrink-0">
                                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 36px; height: 36px;">
                                        <span class="fw-bold">2</span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="fw-bold mb-1">Set Up Shipping</h6>
                                    <p class="small text-muted mb-0">Configure your shipping options and rates</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="d-flex mb-3">
                                <div class="flex-shrink-0">
                                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 36px; height: 36px;">
                                        <span class="fw-bold">3</span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="fw-bold mb-1">Customize Your Store</h6>
                                    <p class="small text-muted mb-0">Add your logo, banner, and store description</p>
                                </div>
                            </div>
                            
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 36px; height: 36px;">
                                        <span class="fw-bold">4</span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="fw-bold mb-1">Promote Your Products</h6>
                                    <p class="small text-muted mb-0">Create discounts and promotions to attract customers</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-light border">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle text-primary fa-lg mt-1"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <p class="mb-0">Need help setting up your store? Check out our <a href="#" class="fw-bold">Vendor Guide</a> or contact our <a href="#" class="fw-bold">Support Team</a>.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .nav-pills .nav-link.active {
        background-color: #212529;
    }
</style>
@endpush
