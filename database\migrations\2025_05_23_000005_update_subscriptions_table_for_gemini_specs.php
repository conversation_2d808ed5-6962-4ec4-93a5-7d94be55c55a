<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            // Add vendor_id field
            if (!Schema::hasColumn('subscriptions', 'vendor_id')) {
                $table->foreignId('vendor_id')->nullable()->after('user_id')->constrained('vendors');
            }
            
            // Add plan_name field
            if (!Schema::hasColumn('subscriptions', 'plan_name')) {
                $table->string('plan_name')->nullable()->after('subscription_plan_id');
            }
            
            // Add amount_paid field
            if (!Schema::hasColumn('subscriptions', 'amount_paid')) {
                $table->decimal('amount_paid', 10, 2)->default(0)->after('plan_name');
            }
            
            // Add paystack_reference field
            if (!Schema::hasColumn('subscriptions', 'paystack_reference')) {
                $table->string('paystack_reference')->nullable()->after('amount_paid');
            }
            
            // Rename is_active to status for consistency with specs
            if (Schema::hasColumn('subscriptions', 'is_active') && !Schema::hasColumn('subscriptions', 'status')) {
                $table->enum('status', ['active', 'inactive', 'pending_payment', 'expired'])
                      ->default('pending_payment')
                      ->after('paystack_reference');
                      
                // We'll handle the data migration separately
                $table->dropColumn('is_active');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropColumn([
                'vendor_id',
                'plan_name',
                'amount_paid',
                'paystack_reference',
                'status'
            ]);
            
            $table->boolean('is_active')->default(true)->after('end_date');
        });
    }
};
