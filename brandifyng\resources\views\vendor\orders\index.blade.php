@extends('layouts.vendor')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0 fw-bold">Orders Management</h2>
        
        <div class="d-flex align-items-center">
            <div class="dropdown me-2">
                <button class="btn btn-outline-dark dropdown-toggle" type="button" id="orderFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    Filter by Status
                </button>
                <ul class="dropdown-menu" aria-labelledby="orderFilterDropdown">
                    <li><a class="dropdown-item" href="{{ route('vendor.orders.index') }}">All Orders</a></li>
                    <li><a class="dropdown-item" href="{{ route('vendor.orders.index', ['status' => 'pending']) }}">Pending</a></li>
                    <li><a class="dropdown-item" href="{{ route('vendor.orders.index', ['status' => 'processing']) }}">Processing</a></li>
                    <li><a class="dropdown-item" href="{{ route('vendor.orders.index', ['status' => 'shipping']) }}">Shipping</a></li>
                    <li><a class="dropdown-item" href="{{ route('vendor.orders.index', ['status' => 'completed']) }}">Completed</a></li>
                    <li><a class="dropdown-item" href="{{ route('vendor.orders.index', ['status' => 'cancelled']) }}">Cancelled</a></li>
                </ul>
            </div>
            
            <form action="{{ route('vendor.orders.index') }}" method="GET" class="d-flex">
                <input type="text" name="search" class="form-control me-2" placeholder="Search by ID or Customer" value="{{ request('search') }}">
                <button type="submit" class="btn btn-dark">Search</button>
            </form>
        </div>
    </div>
    
    <div class="card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="bg-light">
                        <tr>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Date</th>
                            <th>Products</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th class="text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($orders as $order)
                            <tr>
                                <td>#{{ $order->id }}</td>
                                <td>{{ $order->user->name ?? 'Guest' }}</td>
                                <td>{{ $order->created_at->format('M d, Y') }}</td>
                                <td>
                                    @php
                                        $vendorItems = $order->items->filter(function($item) {
                                            return $item->product && $item->product->vendor_id == auth()->user()->vendor->id;
                                        });
                                        $count = $vendorItems->count();
                                    @endphp
                                    {{ $count }} {{ Str::plural('item', $count) }}
                                </td>
                                <td>
                                    @php
                                        $orderTotal = 0;
                                        foreach($vendorItems as $item) {
                                            $orderTotal += $item->price * $item->quantity;
                                        }
                                    @endphp
                                    ${{ number_format($orderTotal, 2) }}
                                </td>
                                <td>
                                    @if($order->status == 'completed')
                                        <span class="badge bg-success">Completed</span>
                                    @elseif($order->status == 'processing')
                                        <span class="badge bg-warning text-dark">Processing</span>
                                    @elseif($order->status == 'shipping')
                                        <span class="badge bg-info">Shipping</span>
                                    @elseif($order->status == 'cancelled')
                                        <span class="badge bg-danger">Cancelled</span>
                                    @elseif($order->status == 'pending')
                                        <span class="badge bg-secondary">Pending</span>
                                    @else
                                        <span class="badge bg-secondary">{{ ucfirst($order->status) }}</span>
                                    @endif
                                </td>
                                <td class="text-end">
                                    <a href="{{ route('vendor.orders.show', $order->id) }}" class="btn btn-sm btn-outline-dark">View</a>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                        <h5>No Orders Found</h5>
                                        <p class="text-muted">You don't have any orders matching your criteria yet.</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div class="mt-4">
        {{ $orders->links() }}
    </div>
</div>
@endsection
