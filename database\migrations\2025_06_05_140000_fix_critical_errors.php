<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix 1: Add brand_description and brand_logo columns to vendors table
        Schema::table('vendors', function (Blueprint $table) {
            if (!Schema::hasColumn('vendors', 'brand_description')) {
                $table->text('brand_description')->nullable()->after('description');
            }
            if (!Schema::hasColumn('vendors', 'brand_logo')) {
                $table->string('brand_logo')->nullable()->after('logo');
            }
        });

        // Fix 2: Rename price to price_at_purchase in order_items table if needed
        if (Schema::hasColumn('order_items', 'price') && !Schema::hasColumn('order_items', 'price_at_purchase')) {
            Schema::table('order_items', function (Blueprint $table) {
                $table->renameColumn('price', 'price_at_purchase');
            });
        }

        // Add missing columns to order_items if they don't exist
        Schema::table('order_items', function (Blueprint $table) {
            if (!Schema::hasColumn('order_items', 'vendor_id')) {
                $table->foreignId('vendor_id')->nullable()->after('order_id')->constrained('vendors');
            }
            if (!Schema::hasColumn('order_items', 'product_name')) {
                $table->string('product_name')->nullable()->after('product_id');
            }
            if (!Schema::hasColumn('order_items', 'product_sku')) {
                $table->string('product_sku')->nullable()->after('product_name');
            }
            if (!Schema::hasColumn('order_items', 'unit_price')) {
                $table->decimal('unit_price', 10, 2)->nullable()->after('price_at_purchase');
            }
            if (!Schema::hasColumn('order_items', 'subtotal')) {
                $table->decimal('subtotal', 10, 2)->nullable()->after('unit_price');
            }
            if (!Schema::hasColumn('order_items', 'status')) {
                $table->string('status')->default('pending')->after('subtotal');
            }
            if (!Schema::hasColumn('order_items', 'commission_rate_snapshot')) {
                $table->decimal('commission_rate_snapshot', 5, 4)
                      ->default(0.027)
                      ->after('status')
                      ->comment('Commission rate at time of purchase (e.g., 0.027 for 2.7%)');
            }
            if (!Schema::hasColumn('order_items', 'commission_amount_calculated')) {
                $table->decimal('commission_amount_calculated', 10, 2)
                      ->default(0)
                      ->after('commission_rate_snapshot')
                      ->comment('Calculated commission amount for this item');
            }
        });

        // Update vendors table with subscription fields if missing
        Schema::table('vendors', function (Blueprint $table) {
            if (!Schema::hasColumn('vendors', 'approved')) {
                // Rename is_approved to approved if it exists
                if (Schema::hasColumn('vendors', 'is_approved')) {
                    $table->renameColumn('is_approved', 'approved');
                } else {
                    $table->boolean('approved')->default(false)->after('logo');
                }
            }
            if (!Schema::hasColumn('vendors', 'subscription_status')) {
                $table->enum('subscription_status', ['active', 'inactive', 'pending_payment', 'expired'])
                      ->default('pending_payment')
                      ->after('approved');
            }
            if (!Schema::hasColumn('vendors', 'orders_processed')) {
                $table->integer('orders_processed')->default(0)->after('subscription_status');
            }
            if (!Schema::hasColumn('vendors', 'free_order_limit')) {
                $table->integer('free_order_limit')->default(10)->after('orders_processed');
            }
            if (!Schema::hasColumn('vendors', 'subscription_required')) {
                $table->boolean('subscription_required')->default(false)->after('free_order_limit');
            }
            if (!Schema::hasColumn('vendors', 'subscription_started_at')) {
                $table->timestamp('subscription_started_at')->nullable()->after('subscription_required');
            }
            if (!Schema::hasColumn('vendors', 'subscription_expires_at')) {
                $table->timestamp('subscription_expires_at')->nullable()->after('subscription_started_at');
            }
            if (!Schema::hasColumn('vendors', 'monthly_subscription_fee')) {
                $table->decimal('monthly_subscription_fee', 8, 2)->default(5000.00)->after('subscription_expires_at');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove brand fields from vendors
        Schema::table('vendors', function (Blueprint $table) {
            if (Schema::hasColumn('vendors', 'brand_description')) {
                $table->dropColumn('brand_description');
            }
            if (Schema::hasColumn('vendors', 'brand_logo')) {
                $table->dropColumn('brand_logo');
            }
        });

        // Rename price_at_purchase back to price in order_items
        if (Schema::hasColumn('order_items', 'price_at_purchase') && !Schema::hasColumn('order_items', 'price')) {
            Schema::table('order_items', function (Blueprint $table) {
                $table->renameColumn('price_at_purchase', 'price');
            });
        }

        // Remove additional order_items columns
        Schema::table('order_items', function (Blueprint $table) {
            $table->dropColumn([
                'vendor_id', 'product_name', 'product_sku', 'unit_price', 
                'subtotal', 'status', 'commission_rate_snapshot', 'commission_amount_calculated'
            ]);
        });

        // Remove subscription fields from vendors
        Schema::table('vendors', function (Blueprint $table) {
            $table->dropColumn([
                'subscription_status', 'orders_processed', 'free_order_limit',
                'subscription_required', 'subscription_started_at', 'subscription_expires_at',
                'monthly_subscription_fee'
            ]);
            
            // Rename approved back to is_approved
            if (Schema::hasColumn('vendors', 'approved')) {
                $table->renameColumn('approved', 'is_approved');
            }
        });
    }
};
