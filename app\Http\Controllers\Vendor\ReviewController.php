<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Review;
use Illuminate\Http\Request;

class ReviewController extends Controller
{
    /**
     * Display a listing of reviews for vendor's products.
     */
    public function index(Request $request)
    {
        $vendor = auth()->user()->vendor;
        
        $query = Review::where('vendor_id', $vendor->id)
            ->with(['user', 'product'])
            ->orderBy('created_at', 'desc');
        
        // Filter by status if requested
        if ($request->has('status')) {
            switch ($request->status) {
                case 'pending':
                    $query->where('is_approved', false);
                    break;
                case 'approved':
                    $query->where('is_approved', true);
                    break;
                case 'responded':
                    $query->whereNotNull('vendor_response');
                    break;
                case 'not_responded':
                    $query->whereNull('vendor_response')->where('is_approved', true);
                    break;
            }
        }
        
        $reviews = $query->paginate(15);
        
        return view('vendor.reviews.index', compact('reviews'));
    }

    /**
     * Show the form for responding to a review.
     */
    public function show(Review $review)
    {
        $vendor = auth()->user()->vendor;
        
        // Make sure this review belongs to the vendor
        if ($review->vendor_id !== $vendor->id) {
            abort(403, 'You do not have access to this review.');
        }
        
        return view('vendor.reviews.show', compact('review'));
    }

    /**
     * Store vendor response to a review.
     */
    public function respond(Request $request, Review $review)
    {
        $vendor = auth()->user()->vendor;
        
        // Make sure this review belongs to the vendor
        if ($review->vendor_id !== $vendor->id) {
            abort(403, 'You do not have access to this review.');
        }
        
        $request->validate([
            'vendor_response' => 'required|string|max:1000',
        ]);
        
        $review->update([
            'vendor_response' => $request->vendor_response,
            'vendor_response_date' => now(),
        ]);
        
        return back()->with('success', 'Your response has been posted successfully.');
    }

    /**
     * Update vendor response to a review.
     */
    public function updateResponse(Request $request, Review $review)
    {
        $vendor = auth()->user()->vendor;
        
        // Make sure this review belongs to the vendor
        if ($review->vendor_id !== $vendor->id) {
            abort(403, 'You do not have access to this review.');
        }
        
        $request->validate([
            'vendor_response' => 'required|string|max:1000',
        ]);
        
        $review->update([
            'vendor_response' => $request->vendor_response,
            'vendor_response_date' => now(),
        ]);
        
        return back()->with('success', 'Your response has been updated successfully.');
    }
}
