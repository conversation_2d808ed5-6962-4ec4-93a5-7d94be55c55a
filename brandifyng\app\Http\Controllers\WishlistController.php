<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Wishlist;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WishlistController extends Controller
{
    /**
     * Display the user's wishlist.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $user = Auth::user();
        return view('customer.wishlist');
    }

    /**
     * Add a product to the user's wishlist.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Product  $product
     * @return \Illuminate\Http\Response
     */
    public function add(Request $request, Product $product)
    {
        $user = Auth::user();
        
        // Check if product already exists in wishlist
        $existingWishlist = $user->wishlist()
            ->where('product_id', $product->id)
            ->first();
            
        if (!$existingWishlist) {
            $wishlist = new Wishlist([
                'product_id' => $product->id
            ]);
            
            $user->wishlist()->save($wishlist);
            
            return redirect()->back()->with('success', 'Product added to your wishlist.');
        }
        
        return redirect()->back()->with('info', 'This product is already in your wishlist.');
    }

    /**
     * Remove a product from the user's wishlist.
     *
     * @param  \App\Models\Wishlist  $wishlist
     * @return \Illuminate\Http\Response
     */
    public function remove(Wishlist $wishlist)
    {
        $user = Auth::user();
        
        if ($wishlist->user_id == $user->id) {
            $wishlist->delete();
            return redirect()->back()->with('success', 'Product removed from your wishlist.');
        }
        
        return redirect()->back()->with('error', 'You do not have permission to remove this item.');
    }

    /**
     * Clear all items from the user's wishlist.
     *
     * @return \Illuminate\Http\Response
     */
    public function clear()
    {
        $user = Auth::user();
        $user->wishlist()->delete();
        
        return redirect()->back()->with('success', 'Your wishlist has been cleared.');
    }
}
