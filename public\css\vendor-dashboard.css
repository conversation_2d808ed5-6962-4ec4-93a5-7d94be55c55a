/**
 * Vendor Dashboard Specific Styles
 * Improves the responsiveness and visual appeal of the vendor dashboard
 */

/* Chart container adjustments */
.chart-container {
    position: relative;
    height: 300px;
    max-height: 300px;
    width: 100%;
    overflow: hidden;
}

.chart-container.small {
    height: 230px;
    max-height: 230px;
}

/* Map container styles */
#nigeria-map-container {
    position: relative;
    height: 380px;
    max-height: 380px;
    overflow: hidden;
    border-bottom: 1px solid #dee2e6;
}

/* Dashboard cards */
.dashboard-stat-card {
    transition: all 0.2s ease;
    border-radius: 0.5rem;
    overflow: hidden;
}

.dashboard-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Stats icon styling */
.stat-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: var(--gray-100);
}

/* Mobile responsiveness improvements */
@media (max-width: 991.98px) {
    .chart-container, 
    .chart-container.small {
        height: 250px;
    }
    
    #nigeria-map-container {
        height: 300px;
    }
    
    .dashboard-stat-card:hover {
        transform: none;
    }
}

@media (max-width: 767.98px) {
    .chart-container, 
    .chart-container.small {
        height: 200px;
    }
    
    #nigeria-map-container {
        height: 250px;
    }
    
    .container-fluid {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* Table adjustments for mobile */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table th, .table td {
    white-space: nowrap;
}

@media (max-width: 576px) {
    .table th, .table td {
        padding: 0.5rem;
        font-size: 0.875rem;
    }
}
