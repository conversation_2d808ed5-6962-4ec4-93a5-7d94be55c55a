<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OrderController extends Controller
{
    /**
     * Display a listing of the vendor's orders.
     */
    public function index(Request $request)
    {
        $vendor = auth()->user()->vendor;
        
        $query = Order::whereHas('items.product', function($query) use ($vendor) {
            $query->where('vendor_id', $vendor->id);
        });
        
        // Filter by status if requested
        if ($request->has('status') && $request->status != 'all') {
            $query->where('status', $request->status);
        }
        
        // Order by created_at desc
        $query->orderBy('created_at', 'desc');
        
        $orders = $query->paginate(15);
        
        return view('vendor.orders.index', compact('orders'));
    }
    
    /**
     * Display the specified order.
     */
    public function show(Order $order)
    {
        $vendor = auth()->user()->vendor;
        
        // Make sure the order contains products from this vendor
        $vendorItems = $order->items()->whereHas('product', function($query) use ($vendor) {
            $query->where('vendor_id', $vendor->id);
        })->get();
        
        if ($vendorItems->isEmpty()) {
            abort(403, 'Unauthorized action.');
        }
        
        // Calculate vendor subtotal for this order
        $vendorSubtotal = $vendorItems->sum(function($item) {
            return $item->price * $item->quantity;
        });
        
        // Get commission rate from the database or use a default
        $commissionRate = 10; // Default 10%
        $commission = ($vendorSubtotal * $commissionRate) / 100;
        $vendorTotal = $vendorSubtotal - $commission;
        
        return view('vendor.orders.show', compact(
            'order', 'vendorItems', 'vendorSubtotal', 'commission', 'vendorTotal'
        ));
    }
    
    /**
     * Update the order status.
     */
    public function updateStatus(Request $request, Order $order)
    {
        $vendor = auth()->user()->vendor;
        
        // Make sure the order contains products from this vendor
        $hasVendorItems = $order->items()->whereHas('product', function($query) use ($vendor) {
            $query->where('vendor_id', $vendor->id);
        })->exists();
        
        if (!$hasVendorItems) {
            abort(403, 'Unauthorized action.');
        }
        
        $request->validate([
            'status' => 'required|in:pending,processing,completed,cancelled'
        ]);
        
        // Update the order status
        $order->status = $request->status;
        $order->save();
        
        return redirect()->back()->with('success', 'Order status updated successfully.');
    }
}
