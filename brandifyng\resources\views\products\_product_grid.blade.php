<div class="row g-4">
    @forelse($products as $product)
        <div class="col-12 col-sm-6 col-md-4">
            <div class="card product-card h-100">
                <a href="{{ route('products.show', $product->slug) }}">
                    <img src="{{ $product->image_url ?? asset('images/default-product.png') }}" class="card-img-top" alt="{{ $product->name }}">
                </a>
                <div class="card-body d-flex flex-column">
                    <h5 class="card-title mb-1">
                        <a href="{{ route('products.show', $product->slug) }}" class="text-dark text-decoration-none stretched-link">{{ Str::limit($product->name, 45) }}</a>
                    </h5>
                    <p class="card-text text-muted small mb-1">
                        <a href="{{ route('vendors.storefront', $product->vendor->slug) }}" class="text-muted text-decoration-none">{{ $product->vendor->shop_name ?? 'N/A' }}</a>
                    </p>
                    <p class="card-text text-muted small mb-2">
                        <a href="{{ route('products.category', $product->category->slug) }}" class="text-muted text-decoration-none">{{ $product->category->name ?? 'N/A' }}</a>
                    </p>
                    
                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold fs-5 text-dark">₦{{ number_format($product->price, 2) }}</span>
                            {{-- Add to wishlist button or rating if available --}}
                        </div>
                        <form action="{{ route('cart.add', $product->id) }}" method="POST" class="ajax-add-to-cart-form">
                            @csrf
                            <input type="hidden" name="quantity" value="1">
                            <button type="submit" class="btn btn-dark w-100 btn-sm add-to-cart-btn">
                                <i class="fas fa-shopping-cart me-1"></i> Add to Cart
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    @empty
        <div class="col-12">
            <div class="alert alert-light text-center py-5">
                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                <h4 class="alert-heading">No Products Found</h4>
                <p class="text-muted">Sorry, we couldn't find any products matching your criteria. Try adjusting your search or filters, or check back later!</p>
                <a href="{{ route('products.index') }}" class="btn btn-primary mt-3">Reset Filters</a>
            </div>
        </div>
    @endforelse
</div>

@if($products->hasPages())
<div class="mt-5 d-flex justify-content-center">
    {{ $products->appends(request()->query())->links() }} {{-- Ensure filters are kept during pagination --}}
</div>
@endif
