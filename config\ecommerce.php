<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Platform Commission Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration defines the commission rates and settings for the
    | multivendor e-commerce platform.
    |
    */

    'commission' => [
        'rate' => env('PLATFORM_COMMISSION_RATE', 0.027), // 2.7% default commission
        'currency' => 'NGN',
        'currency_symbol' => '₦',
    ],

    /*
    |--------------------------------------------------------------------------
    | Delivery Zone Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Nigerian delivery zones and their respective
    | subscription fees and delivery policies.
    |
    */

    'delivery_zones' => [
        'platform_delivery_zone' => [
            'states' => ['Lagos', 'Abuja', 'Ibadan', 'Akure'],
            'subscription_fee' => 10000, // NGN
            'delivery_days' => ['Monday', 'Wednesday', 'Friday'],
            'description' => 'Platform handles delivery for these states'
        ],
        'other_states_zone' => [
            'subscription_fee' => 7000, // NGN
            'description' => 'Vendor handles their own delivery'
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Subscription Plans Configuration
    |--------------------------------------------------------------------------
    |
    | Default subscription plan settings based on delivery zones.
    |
    */

    'subscription_plans' => [
        'platform_delivery_zone' => [
            'name' => 'Platform Delivery Zone',
            'price' => 10000,
            'duration_days' => 30,
            'features' => [
                'Platform handles delivery',
                'Delivery on Mon, Wed, Fri',
                'Access to all platform features',
                'Customer support'
            ]
        ],
        'other_states_zone' => [
            'name' => 'Other States Zone',
            'price' => 7000,
            'duration_days' => 30,
            'features' => [
                'Self-managed delivery',
                'Access to all platform features',
                'Customer support'
            ]
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Order Status Configuration
    |--------------------------------------------------------------------------
    |
    | Available order statuses and their descriptions.
    |
    */

    'order_statuses' => [
        'pending' => 'Order placed, awaiting payment',
        'processing' => 'Payment confirmed, preparing order',
        'shipped' => 'Order shipped',
        'awaiting_platform_pickup' => 'Awaiting platform pickup for delivery',
        'out_for_delivery' => 'Out for delivery',
        'delivered' => 'Order delivered',
        'cancelled' => 'Order cancelled',
        'refunded' => 'Order refunded'
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Configuration
    |--------------------------------------------------------------------------
    |
    | Payment gateway settings and configurations.
    |
    */

    'payment' => [
        'default_gateway' => 'paystack',
        'supported_gateways' => ['paystack'],
        'currency' => 'NGN',
        'webhook_tolerance' => 300, // seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Shipping Configuration
    |--------------------------------------------------------------------------
    |
    | Default shipping settings and ShipBubbles integration.
    |
    */

    'shipping' => [
        'default_weight' => 1.0, // kg
        'default_dimensions' => [
            'length' => 20, // cm
            'width' => 15,  // cm
            'height' => 10  // cm
        ],
        'shipbubbles' => [
            'enabled' => env('SHIPBUBBLES_ENABLED', true),
            'api_url' => env('SHIPBUBBLES_API_URL', 'https://api.shipbubble.com/v1'),
            'timeout' => 30, // seconds
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Nigerian Market Specific Settings
    |--------------------------------------------------------------------------
    |
    | Settings specific to the Nigerian market.
    |
    */

    'nigerian_market' => [
        'phone_validation_pattern' => '/^(\+234|234|0)[789][01]\d{8}$/',
        'states' => [
            'Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa',
            'Benue', 'Borno', 'Cross River', 'Delta', 'Ebonyi', 'Edo',
            'Ekiti', 'Enugu', 'Gombe', 'Imo', 'Jigawa', 'Kaduna',
            'Kano', 'Katsina', 'Kebbi', 'Kogi', 'Kwara', 'Lagos',
            'Nasarawa', 'Niger', 'Ogun', 'Ondo', 'Osun', 'Oyo',
            'Plateau', 'Rivers', 'Sokoto', 'Taraba', 'Yobe', 'Zamfara',
            'Abuja' // FCT
        ],
        'default_country' => 'Nigeria',
        'timezone' => 'Africa/Lagos'
    ]
];
