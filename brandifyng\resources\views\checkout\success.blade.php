@extends('layouts.app')

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm text-center p-4">
                <div class="mb-4">
                    <div class="rounded-circle bg-success d-flex align-items-center justify-content-center mx-auto mb-4" style="width: 80px; height: 80px;">
                        <i class="fas fa-check fa-3x text-white"></i>
                    </div>
                    <h1 class="fw-bold mb-3">Thank You!</h1>
                    <p class="lead mb-0">Your order has been placed successfully.</p>
                    <p class="text-muted">Order #{{ $order->order_number }}</p>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header bg-white py-3">
                        <h5 class="fw-bold mb-0">Order Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Quantity</th>
                                        <th class="text-end">Price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($order->items as $item)
                                        <tr>
                                            <td>{{ json_decode($item->product_data)->name }}</td>
                                            <td>{{ $item->quantity }}</td>
                                            <td class="text-end">${{ number_format($item->price, 2) }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <td colspan="2" class="text-end fw-bold">Subtotal:</td>
                                        <td class="text-end">${{ number_format($order->subtotal, 2) }}</td>
                                    </tr>
                                    <tr>
                                        <td colspan="2" class="text-end fw-bold">Tax:</td>
                                        <td class="text-end">${{ number_format($order->tax, 2) }}</td>
                                    </tr>
                                    <tr>
                                        <td colspan="2" class="text-end fw-bold">Shipping:</td>
                                        <td class="text-end">${{ number_format($order->shipping, 2) }}</td>
                                    </tr>
                                    <tr>
                                        <td colspan="2" class="text-end fw-bold">Total:</td>
                                        <td class="text-end fw-bold">${{ number_format($order->total, 2) }}</td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header bg-white py-3">
                        <h5 class="fw-bold mb-0">Shipping Information</h5>
                    </div>
                    <div class="card-body">
                        @php
                            $address = json_decode($order->shipping_address);
                        @endphp
                        <div class="row">
                            <div class="col-md-6 text-start">
                                <p class="mb-1"><strong>Name:</strong> {{ $address->first_name }} {{ $address->last_name }}</p>
                                <p class="mb-1"><strong>Email:</strong> {{ $address->email }}</p>
                                <p class="mb-1"><strong>Phone:</strong> {{ $address->phone }}</p>
                            </div>
                            <div class="col-md-6 text-start">
                                <p class="mb-1"><strong>Address:</strong> {{ $address->address }}</p>
                                <p class="mb-1"><strong>City:</strong> {{ $address->city }}</p>
                                <p class="mb-1"><strong>State:</strong> {{ $address->state }}</p>
                                <p class="mb-1"><strong>Postal Code:</strong> {{ $address->postal_code }}</p>
                                <p class="mb-1"><strong>Country:</strong> {{ $address->country }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header bg-white py-3">
                        <h5 class="fw-bold mb-0">Payment Information</h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-1"><strong>Payment Method:</strong> {{ ucfirst($order->payment_method) }}</p>
                        <p class="mb-1"><strong>Payment Status:</strong> 
                            @if($order->payment_status === 'paid')
                                <span class="badge bg-success">Paid</span>
                            @else
                                <span class="badge bg-warning text-dark">{{ ucfirst($order->payment_status) }}</span>
                            @endif
                        </p>
                        @if($order->payment_details)
                            @php
                                $paymentDetails = json_decode($order->payment_details);
                            @endphp
                            @if(isset($paymentDetails->reference))
                                <p class="mb-1"><strong>Payment Reference:</strong> {{ $paymentDetails->reference }}</p>
                            @endif
                            @if(isset($paymentDetails->paid_at))
                                <p class="mb-1"><strong>Paid At:</strong> {{ $paymentDetails->paid_at }}</p>
                            @endif
                        @endif
                    </div>
                </div>
                
                <div class="d-flex justify-content-center gap-3 mt-4">
                    <a href="{{ route('home') }}" class="btn btn-outline-dark">Continue Shopping</a>
                    <a href="{{ route('orders.show', $order->id) }}" class="btn btn-dark">View Order Details</a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
