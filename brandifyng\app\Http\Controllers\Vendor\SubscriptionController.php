<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Models\Payment;
use Illuminate\Http\Request;
use Carbon\Carbon;

class SubscriptionController extends Controller
{
    /**
     * Display the vendor's subscription information.
     */
    public function index()
    {
        $vendor = auth()->user()->vendor;
        $subscription = $vendor->subscription;
        $plans = SubscriptionPlan::where('is_active', true)->get();
        
        $currentPlan = null;
        $daysRemaining = 0;
        $isExpired = false;
        
        if ($subscription) {
            $currentPlan = $subscription->plan;
            $daysRemaining = Carbon::now()->diffInDays($subscription->end_date, false);
            $isExpired = $daysRemaining <= 0;
        }
        
        // Get payment history
        $payments = Payment::where('payable_type', Subscription::class)
            ->whereHas('payable', function($query) use ($vendor) {
                $query->where('vendor_id', $vendor->id);
            })
            ->latest()
            ->paginate(5);
            
        return view('vendor.subscription.index', compact(
            'subscription', 'plans', 'currentPlan', 'daysRemaining', 'isExpired', 'payments'
        ));
    }
    
    /**
     * Show the form for subscribing to a new plan.
     */
    public function create(SubscriptionPlan $plan)
    {
        return view('vendor.subscription.create', compact('plan'));
    }
    
    /**
     * Process the subscription purchase.
     */
    public function store(Request $request, SubscriptionPlan $plan)
    {
        $request->validate([
            'payment_method' => 'required|in:paystack',
        ]);
        
        $vendor = auth()->user()->vendor;
        
        // Check if vendor already has an active subscription
        $existingSubscription = $vendor->subscription;
        
        // Calculate new end date
        $startDate = Carbon::now();
        $endDate = clone $startDate;
        
        // Add duration based on plan type
        if ($plan->duration_type === 'monthly') {
            $endDate->addMonth();
        } elseif ($plan->duration_type === 'yearly') {
            $endDate->addYear();
        } elseif (is_numeric($plan->duration_days)) {
            $endDate->addDays($plan->duration_days);
        }
        
        // If there's an existing subscription, extend it instead of creating a new one
        if ($existingSubscription) {
            // If expired, start from today, otherwise extend current expiration
            if (Carbon::now()->gt($existingSubscription->end_date)) {
                $existingSubscription->end_date = $endDate;
            } else {
                $existingSubscription->end_date = Carbon::parse($existingSubscription->end_date)->add(
                    $endDate->diff($startDate)
                );
            }
            
            $existingSubscription->plan_id = $plan->id;
            $existingSubscription->save();
            $subscription = $existingSubscription;
        } else {
            // Create a new subscription
            $subscription = Subscription::create([
                'vendor_id' => $vendor->id,
                'plan_id' => $plan->id,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'status' => 'active',
            ]);
        }
        
        // Create a payment record
        $payment = Payment::create([
            'payable_type' => Subscription::class,
            'payable_id' => $subscription->id,
            'amount' => $plan->price,
            'payment_method' => $request->payment_method,
            'status' => 'pending', // In a real app, this would be set after payment processing
            'transaction_id' => 'TXN-' . uniqid(),
        ]);
        
        // In a real application, you would integrate with a payment gateway here
        // For this demo, we'll just simulate a successful payment
        $payment->status = 'completed';
        $payment->save();
        
        return redirect()->route('vendor.subscription.index')
            ->with('success', 'Subscription purchased successfully! Your subscription is now active.');
    }
    
    /**
     * Cancel the vendor's subscription.
     */
    public function cancel(Request $request)
    {
        $vendor = auth()->user()->vendor;
        $subscription = $vendor->subscription;
        
        if (!$subscription) {
            return redirect()->route('vendor.subscription.index')
                ->with('error', 'You don\'t have an active subscription to cancel.');
        }
        
        // In a real app, you might want to add confirmation
        $subscription->status = 'cancelled';
        $subscription->save();
        
        return redirect()->route('vendor.subscription.index')
            ->with('success', 'Your subscription has been cancelled. You can still use it until the end date.');
    }
}
