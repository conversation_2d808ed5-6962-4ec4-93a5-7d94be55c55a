@extends('layouts.vendor')

@section('title', 'Subscription Plans')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Subscription Status Alert -->
            @if($subscriptionDetails['needs_subscription'])
                <div class="alert alert-warning mb-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                        <div>
                            <h5 class="alert-heading mb-1">Subscription Required</h5>
                            <p class="mb-0">
                                You have processed {{ $subscriptionDetails['orders_processed'] }} orders and reached your free limit. 
                                Please subscribe to continue processing orders.
                            </p>
                        </div>
                    </div>
                </div>
            @else
                <div class="alert alert-info mb-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle fa-2x me-3"></i>
                        <div>
                            <h5 class="alert-heading mb-1">Free Orders Remaining</h5>
                            <p class="mb-0">
                                You have {{ $subscriptionDetails['free_orders_remaining'] }} free orders remaining. 
                                Subscribe now to get unlimited order processing!
                            </p>
                        </div>
                    </div>
                </div>
            @endif

            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Choose Your Subscription Plan</h1>
                    <p class="text-muted">Unlock unlimited order processing and premium features</p>
                </div>
                <a href="{{ route('vendor.dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
                </a>
            </div>

            <!-- Subscription Plans -->
            <div class="row">
                @foreach($plans as $index => $plan)
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 {{ $plan['recommended'] ? 'border-primary' : '' }} position-relative">
                            @if($plan['recommended'])
                                <div class="position-absolute top-0 start-50 translate-middle">
                                    <span class="badge bg-primary px-3 py-2">Recommended</span>
                                </div>
                            @endif
                            
                            <div class="card-header text-center {{ $plan['recommended'] ? 'bg-primary text-white' : 'bg-light' }}">
                                <h4 class="card-title mb-0">{{ $plan['name'] }}</h4>
                            </div>
                            
                            <div class="card-body text-center">
                                <div class="mb-4">
                                    <span class="display-4 fw-bold">${{ $plan['price'] }}</span>
                                    <span class="text-muted">/ {{ $plan['duration'] }} month{{ $plan['duration'] > 1 ? 's' : '' }}</span>
                                </div>
                                
                                <ul class="list-unstyled text-start">
                                    @foreach($plan['features'] as $feature)
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success me-2"></i>
                                            {{ $feature }}
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                            
                            <div class="card-footer">
                                <form action="{{ route('vendor.subscription.subscribe') }}" method="POST" class="subscription-form">
                                    @csrf
                                    <input type="hidden" name="plan" value="{{ strtolower(str_replace(' Plan', '', $plan['name'])) }}">
                                    <input type="hidden" name="payment_method" value="demo">
                                    
                                    <button type="submit" class="btn {{ $plan['recommended'] ? 'btn-primary' : 'btn-outline-primary' }} w-100 btn-lg">
                                        <i class="fas fa-credit-card me-2"></i>
                                        Subscribe Now
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Features Comparison -->
            <div class="card mt-5">
                <div class="card-header">
                    <h5 class="mb-0">Why Subscribe?</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-success">✓ With Subscription</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Unlimited order processing</li>
                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Advanced analytics dashboard</li>
                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Priority customer support</li>
                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Marketing tools access</li>
                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Featured vendor listing</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-danger">✗ Without Subscription</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="fas fa-times text-danger me-2"></i> Limited to {{ $vendor->free_order_limit }} orders only</li>
                                <li class="mb-2"><i class="fas fa-times text-danger me-2"></i> Basic analytics only</li>
                                <li class="mb-2"><i class="fas fa-times text-danger me-2"></i> Standard support</li>
                                <li class="mb-2"><i class="fas fa-times text-danger me-2"></i> No marketing tools</li>
                                <li class="mb-2"><i class="fas fa-times text-danger me-2"></i> Standard listing</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- FAQ Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Frequently Asked Questions</h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    What happens after my free orders are used up?
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    After processing {{ $vendor->free_order_limit }} orders, you'll need to subscribe to continue processing new orders. Your existing orders and products remain active.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    Can I cancel my subscription anytime?
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Yes, you can cancel your subscription at any time. You'll continue to have access until the end of your current billing period.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    What payment methods do you accept?
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    We accept all major credit cards, PayPal, and bank transfers. All payments are processed securely.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle subscription form submissions
    const subscriptionForms = document.querySelectorAll('.subscription-form');
    
    subscriptionForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            
            // Re-enable button after 3 seconds if form doesn't submit
            setTimeout(() => {
                if (submitBtn.disabled) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }
            }, 3000);
        });
    });
});
</script>
@endpush
@endsection
