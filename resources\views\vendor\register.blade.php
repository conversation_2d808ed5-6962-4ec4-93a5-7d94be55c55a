@extends('layouts.app')

@section('content')
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="text-center mb-5">
                    <h1 class="fw-bold mb-2">Become a Vendor</h1>
                    <p class="text-muted">Join our marketplace and start selling your products to thousands of customers.</p>
                </div>

                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white">
                        <ul class="nav nav-pills card-header-pills">
                            <li class="nav-item">
                                <a class="nav-link active" href="#registration" data-bs-toggle="tab">
                                    <i class="fas fa-user-plus me-2"></i> Registration
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link disabled" href="#verification" data-bs-toggle="tab">
                                    <i class="fas fa-check-circle me-2"></i> Verification
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link disabled" href="#setup" data-bs-toggle="tab">
                                    <i class="fas fa-store me-2"></i> Store Setup
                                </a>
                            </li>
                        </ul>
                    </div>

                    <div class="card-body p-4">
                        <div class="tab-content">
                            <div class="tab-pane fade show active" id="registration">
                                <form method="POST" action="{{ route('vendor.store') }}" enctype="multipart/form-data">
                                    @csrf

                                    <div class="row mb-4">
                                        <div class="col-12">
                                            <h5 class="fw-bold mb-3">Personal Information</h5>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="name" class="form-label">Full Name <span
                                                    class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                                id="name" name="name" value="{{ old('name') }}" required>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label">Email Address <span
                                                    class="text-danger">*</span></label>
                                            <input type="email" class="form-control @error('email') is-invalid @enderror"
                                                id="email" name="email" value="{{ old('email') }}" required>
                                            @error('email')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="password" class="form-label">Password <span
                                                    class="text-danger">*</span></label>
                                            <input type="password"
                                                class="form-control @error('password') is-invalid @enderror" id="password"
                                                name="password" required>
                                            @error('password')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="text-muted">Password must be at least 8 characters long</small>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="password_confirmation" class="form-label">Confirm Password <span
                                                    class="text-danger">*</span></label>
                                            <input type="password" class="form-control" id="password_confirmation"
                                                name="password_confirmation" required>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="contact_phone" class="form-label">Phone Number <span
                                                    class="text-danger">*</span></label>
                                            <input type="text"
                                                class="form-control @error('contact_phone') is-invalid @enderror"
                                                id="contact_phone" name="contact_phone" value="{{ old('contact_phone') }}"
                                                placeholder="+234XXXXXXXXXX" required>
                                            @error('contact_phone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="text-muted">Enter Nigerian phone number (e.g.,
                                                +234XXXXXXXXXX)</small>
                                        </div>
                                    </div>

                                    <div class="row mb-4">
                                        <div class="col-12">
                                            <h5 class="fw-bold mb-3">Business Information</h5>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="business_name" class="form-label">Business Name <span
                                                    class="text-danger">*</span></label>
                                            <input type="text"
                                                class="form-control @error('business_name') is-invalid @enderror"
                                                id="business_name" name="business_name" value="{{ old('business_name') }}"
                                                required>
                                            @error('business_name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="contact_email_business" class="form-label">Business Email</label>
                                            <input type="email"
                                                class="form-control @error('contact_email_business') is-invalid @enderror"
                                                id="contact_email_business" name="contact_email_business"
                                                value="{{ old('contact_email_business') }}"
                                                placeholder="<EMAIL>">
                                            @error('contact_email_business')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="text-muted">Optional: Business contact email (if different from
                                                personal)</small>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="logo" class="form-label">Business Logo</label>
                                            <input type="file" class="form-control @error('logo') is-invalid @enderror"
                                                id="logo" name="logo">
                                            @error('logo')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="text-muted">Recommended size: 400x400px, max 2MB</small>
                                        </div>

                                        <div class="col-12 mb-3">
                                            <label for="address_line1" class="form-label">Business Address <span
                                                    class="text-danger">*</span></label>
                                            <input type="text"
                                                class="form-control @error('address_line1') is-invalid @enderror"
                                                id="address_line1" name="address_line1"
                                                value="{{ old('address_line1') }}"
                                                placeholder="Street address, building number" required>
                                            @error('address_line1')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="city" class="form-label">City <span
                                                    class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('city') is-invalid @enderror"
                                                id="city" name="city" value="{{ old('city') }}" required>
                                            @error('city')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="state" class="form-label">State <span
                                                    class="text-danger">*</span></label>
                                            <select class="form-select @error('state') is-invalid @enderror"
                                                id="state" name="state" required>
                                                <option value="">Select State</option>
                                                @if (isset($nigerianStates))
                                                    @foreach ($nigerianStates as $state)
                                                        <option value="{{ $state }}"
                                                            {{ old('state') == $state ? 'selected' : '' }}>
                                                            {{ $state }}</option>
                                                    @endforeach
                                                @endif
                                            </select>
                                            @error('state')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="col-12 mb-3">
                                            <label for="business_description" class="form-label">Business Description
                                                <span class="text-danger">*</span></label>
                                            <textarea class="form-control @error('business_description') is-invalid @enderror" id="business_description"
                                                name="business_description" rows="4" required>{{ old('business_description') }}</textarea>
                                            @error('business_description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="text-muted">Tell us about your business, products, and what makes
                                                you unique (minimum 100 characters)</small>
                                        </div>
                                    </div>

                                    <!-- Delivery Zone Information -->
                                    <div class="row mb-4" id="delivery-zone-info" style="display: none;">
                                        <div class="col-12">
                                            <div class="alert alert-info">
                                                <h6 class="fw-bold mb-2"><i class="fas fa-info-circle me-2"></i>Delivery
                                                    Zone Information</h6>
                                                <div id="zone-details">
                                                    <!-- Will be populated by JavaScript -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mb-4">
                                        <div class="col-12">
                                            <h5 class="fw-bold mb-3">Verification Documents</h5>
                                            <p class="text-muted small mb-3">These documents are required to verify your
                                                identity and business. They will be securely stored and only accessible to
                                                our verification team.</p>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="id_document" class="form-label">ID Document <span
                                                    class="text-danger">*</span></label>
                                            <input type="file"
                                                class="form-control @error('id_document') is-invalid @enderror"
                                                id="id_document" name="id_document" required>
                                            @error('id_document')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="text-muted">Upload a copy of your ID card, passport, or driver's
                                                license (JPG, PNG, PDF, max 5MB)</small>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="business_document" class="form-label">Business Document</label>
                                            <input type="file"
                                                class="form-control @error('business_document') is-invalid @enderror"
                                                id="business_document" name="business_document">
                                            @error('business_document')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="text-muted">Upload a business registration certificate, license,
                                                or other relevant document (JPG, PNG, PDF, max 5MB)</small>
                                        </div>
                                    </div>

                                    <div class="form-check mb-4">
                                        <input class="form-check-input @error('terms') is-invalid @enderror"
                                            type="checkbox" id="terms" name="terms" required>
                                        <label class="form-check-label" for="terms">
                                            I agree to the <a href="{{ route('terms') }}" target="_blank">Terms of
                                                Service</a> and <a href="{{ route('privacy') }}" target="_blank">Privacy
                                                Policy</a>
                                        </label>
                                        @error('terms')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-dark btn-lg">
                                            Submit Application
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card border-0 shadow-sm mt-4">
                    <div class="card-body p-4">
                        <h5 class="fw-bold mb-3">Why Become a Vendor?</h5>

                        <div class="row g-4">
                            <div class="col-md-4">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <div class="rounded-circle bg-light d-flex align-items-center justify-content-center"
                                            style="width: 48px; height: 48px;">
                                            <i class="fas fa-globe text-dark"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="fw-bold mb-1">Global Reach</h6>
                                        <p class="small text-muted mb-0">Access customers from around the world.</p>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <div class="rounded-circle bg-light d-flex align-items-center justify-content-center"
                                            style="width: 48px; height: 48px;">
                                            <i class="fas fa-credit-card text-dark"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="fw-bold mb-1">Secure Payments</h6>
                                        <p class="small text-muted mb-0">Multiple payment options for your convenience.</p>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <div class="rounded-circle bg-light d-flex align-items-center justify-content-center"
                                            style="width: 48px; height: 48px;">
                                            <i class="fas fa-chart-line text-dark"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="fw-bold mb-1">Sales Analytics</h6>
                                        <p class="small text-muted mb-0">Detailed reports to track your business growth.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style>
        .nav-pills .nav-link.active {
            background-color: #212529;
        }
    </style>
@endpush

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const stateSelect = document.getElementById('state');
            const deliveryZoneInfo = document.getElementById('delivery-zone-info');
            const zoneDetails = document.getElementById('zone-details');

            // Delivery zone configuration
            const deliveryZones = {
                'platform_delivery_zone': {
                    states: @json($platformDeliveryStates ?? ['Lagos', 'Abuja', 'Ibadan', 'Akure']),
                    fee: 10000,
                    description: 'Platform handles delivery for these states',
                    deliveryDays: ['Monday', 'Wednesday', 'Friday']
                },
                'other_states_zone': {
                    fee: 7000,
                    description: 'Vendor handles their own delivery'
                }
            };

            function updateDeliveryZoneInfo() {
                const selectedState = stateSelect.value;

                if (!selectedState) {
                    deliveryZoneInfo.style.display = 'none';
                    return;
                }

                let zoneType = 'other_states_zone';
                let zoneConfig = deliveryZones.other_states_zone;

                if (deliveryZones.platform_delivery_zone.states.includes(selectedState)) {
                    zoneType = 'platform_delivery_zone';
                    zoneConfig = deliveryZones.platform_delivery_zone;
                }

                let html = `
            <p class="mb-2"><strong>Zone Type:</strong> ${zoneType === 'platform_delivery_zone' ? 'Platform Delivery Zone' : 'Other States Zone'}</p>
            <p class="mb-2"><strong>Monthly Subscription Fee:</strong> ₦${zoneConfig.fee.toLocaleString()}</p>
            <p class="mb-2"><strong>Description:</strong> ${zoneConfig.description}</p>
        `;

                if (zoneType === 'platform_delivery_zone') {
                    html +=
                        `<p class="mb-0"><strong>Delivery Days:</strong> ${zoneConfig.deliveryDays.join(', ')}</p>`;
                }

                zoneDetails.innerHTML = html;
                deliveryZoneInfo.style.display = 'block';
            }

            stateSelect.addEventListener('change', updateDeliveryZoneInfo);
        });
    </script>
@endpush
