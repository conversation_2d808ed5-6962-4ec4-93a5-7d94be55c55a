<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>Brandify</title>

    <!-- Bootstrap 5 CSS and other resources -->
    @include('partials.head-bootstrap')
</head>

<body class="font-sans antialiased bg-white text-black min-h-screen flex flex-col">
    <!-- Top Navigation Bar -->
    <header
        class="fixed w-full bg-white bg-opacity-95 backdrop-blur-sm shadow-sm z-50 py-2 px-4 sm:px-6 lg:px-8 transition-all duration-300">
        <div class="container mx-auto flex justify-between items-center">
            <a href="/" class="flex items-center">
                <img src="{{ asset('storage/brandify.png') }}" alt="Brandify Logo" class="h-12">
            </a>
            <div class="flex items-center space-x-8">
                <nav class="hidden md:flex space-x-8">
                    <a href="#"
                        class="text-gray-800 hover:text-black font-medium transition-colors duration-200 relative after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-black hover:after:w-full after:transition-all after:duration-300">Home</a>
                    <a href="#"
                        class="text-gray-800 hover:text-black font-medium transition-colors duration-200 relative after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-black hover:after:w-full after:transition-all after:duration-300">Shop</a>
                    <a href="#"
                        class="text-gray-800 hover:text-black font-medium transition-colors duration-200 relative after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-black hover:after:w-full after:transition-all after:duration-300">About</a>
                    <a href="#"
                        class="text-gray-800 hover:text-black font-medium transition-colors duration-200 relative after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-black hover:after:w-full after:transition-all after:duration-300">Contact</a>
                </nav>
                <div class="flex items-center space-x-6">
                    <a href="#"
                        class="text-gray-800 hover:text-black transition-colors duration-200 relative group">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        <span
                            class="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200">Account</span>
                    </a>
                    <a href="#"
                        class="text-gray-800 hover:text-black transition-colors duration-200 relative group">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <span
                            class="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200">Cart</span>
                    </a>
                    <button
                        class="md:hidden text-gray-800 hover:text-black focus:outline-none transition-colors duration-200">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-4 pt-24 pb-6 flex-grow">
        <!-- Search Bar Section (Moved to top) -->
        <section class="mb-8 pt-4">
            <div class="relative max-w-4xl mx-auto group">
                <input type="text" placeholder="Search for brands, products, and more..."
                    class="w-full p-4 pl-12 pr-10 bg-gray-50 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-black focus:bg-white transition-all duration-300 text-base placeholder-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-black transition-colors duration-300"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <button
                    class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-black transition-colors duration-300 focus:outline-none hidden group-focus-within:block">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </section>

        <main>
            <!-- Hero Slider Section -->
            <section class="mb-16 relative overflow-hidden rounded-2xl bg-gray-100">
                <div class="relative h-[500px] overflow-hidden">
                    <!-- Slider container -->
                    <div class="relative w-full h-full" id="hero-slider">
                        <!-- Slide 1 -->
                        <div class="absolute inset-0 transition-opacity duration-1000 ease-in-out opacity-100" data-slide="1">
                            <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('{{ asset('storage/banner1.jpg') }}');"></div>
                            <div class="absolute inset-0 bg-black bg-opacity-40"></div>
                            <div class="relative max-w-7xl mx-auto h-full flex items-center px-4 sm:px-6 lg:px-8">
                                <div class="text-white max-w-2xl">
                                    <h2 class="text-4xl md:text-5xl font-bold mb-4">Summer Collection 2025</h2>
                                    <p class="text-xl mb-6">Discover the latest trends in fashion and style</p>
                                    <a href="#" class="inline-block bg-white text-black px-8 py-3 rounded-md font-medium hover:bg-gray-100 transition-colors">Shop Now</a>
                                </div>
                            </div>
                        </div>
                        <!-- Slide 2 -->
                        <div class="absolute inset-0 transition-opacity duration-1000 ease-in-out opacity-0" data-slide="2">
                            <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('{{ asset('storage/banner2.jpg') }}');"></div>
                            <div class="absolute inset-0 bg-black bg-opacity-40"></div>
                            <div class="relative max-w-7xl mx-auto h-full flex items-center px-4 sm:px-6 lg:px-8">
                                <div class="text-white max-w-2xl ml-auto text-right">
                                    <h2 class="text-4xl md:text-5xl font-bold mb-4">New Arrivals</h2>
                                    <p class="text-xl mb-6">Fresh styles for the new season</p>
                                    <a href="#" class="inline-block bg-white text-black px-8 py-3 rounded-md font-medium hover:bg-gray-100 transition-colors">Explore</a>
                                </div>
                            </div>
                        </div>
                        <!-- Slide 3 -->
                        <div class="absolute inset-0 transition-opacity duration-1000 ease-in-out opacity-0" data-slide="3">
                            <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('{{ asset('storage/banner3.jpg') }}');"></div>
                            <div class="absolute inset-0 bg-black bg-opacity-40"></div>
                            <div class="relative max-w-7xl mx-auto h-full flex items-center px-4 sm:px-6 lg:px-8">
                                <div class="text-white max-w-2xl">
                                    <h2 class="text-4xl md:text-5xl font-bold mb-4">Exclusive Deals</h2>
                                    <p class="text-xl mb-6">Limited time offers on premium brands</p>
                                    <a href="#" class="inline-block bg-white text-black px-8 py-3 rounded-md font-medium hover:bg-gray-100 transition-colors">View Offers</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Slider Navigation -->
                    <button id="slider-prev" class="absolute left-4 top-1/2 -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 w-10 h-10 rounded-full flex items-center justify-center text-gray-800 transition-all duration-200 z-10">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>
                    <button id="slider-next" class="absolute right-4 top-1/2 -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 w-10 h-10 rounded-full flex items-center justify-center text-gray-800 transition-all duration-200 z-10">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </button>
                    
                    <!-- Slider Dots -->
                    <div class="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2 z-10">
                        <button class="w-3 h-3 rounded-full bg-white bg-opacity-80 hover:bg-opacity-100 transition-all duration-200 slider-dot active" data-slide="0"></button>
                        <button class="w-3 h-3 rounded-full bg-white bg-opacity-80 hover:bg-opacity-100 transition-all duration-200 slider-dot" data-slide="1"></button>
                        <button class="w-3 h-3 rounded-full bg-white bg-opacity-80 hover:bg-opacity-100 transition-all duration-200 slider-dot" data-slide="2"></button>
                    </div>
                </div>
            </section>

            <!-- Featured Brands Section -->
            <section class="mb-16">
                <div class="flex justify-between items-center mb-8">
                    <div class="flex items-center space-x-4">
                        <h2 class="text-3xl font-bold text-black">Featured Brands</h2>
                        <span class="text-gray-400 text-sm font-medium">Trusted by the best</span>
                    </div>
                    <a href="#"
                        class="group inline-flex items-center space-x-2 text-black font-medium hover:text-gray-600 transition-colors duration-300">
                        <span>View All</span>
                        <svg xmlns="http://www.w3.org/2000/svg"
                            class="h-5 w-5 transform group-hover:translate-x-1 transition-transform duration-300"
                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M17 8l4 4m0 0l-4 4m4-4H3" />
                        </svg>
                    </a>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- Featured Brand Cards -->
                    <div
                        class="group relative overflow-hidden rounded-2xl bg-white border-2 border-gray-100 hover:border-black transition-all duration-300 transform hover:-translate-y-1">
                        <div class="relative p-6">
                            <div class="h-40 mb-4 flex items-center justify-center">
                                <img src="/images/brand1-logo.svg" alt="Nike" class="h-16 object-contain">
                            </div>
                            <h3 class="text-xl font-bold mb-2">Nike</h3>
                            <p class="text-gray-600 text-sm mb-4">Leading sportswear and athletic footwear brand known
                                for innovation and performance.</p>
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-500">250+ Products</span>
                                <a href="#" class="text-black hover:underline text-sm font-medium">View
                                    Collection →</a>
                            </div>
                        </div>
                    </div>
                    <div
                        class="group relative overflow-hidden rounded-2xl bg-white border-2 border-gray-100 hover:border-black transition-all duration-300 transform hover:-translate-y-1">
                        <div class="relative p-6">
                            <div class="h-40 mb-4 flex items-center justify-center">
                                <img src="/images/brand2-logo.svg" alt="Adidas" class="h-16 object-contain">
                            </div>
                            <h3 class="text-xl font-bold mb-2">Adidas</h3>
                            <p class="text-gray-600 text-sm mb-4">Iconic sportswear brand combining style with
                                performance for athletes and fashion enthusiasts.</p>
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-500">200+ Products</span>
                                <a href="#" class="text-black hover:underline text-sm font-medium">View
                                    Collection →</a>
                            </div>
                        </div>
                    </div>
                    <div
                        class="group relative overflow-hidden rounded-2xl bg-white border-2 border-gray-100 hover:border-black transition-all duration-300 transform hover:-translate-y-1">
                        <div class="relative p-6">
                            <div class="h-40 mb-4 flex items-center justify-center">
                                <img src="/images/brand3-logo.svg" alt="Puma" class="h-16 object-contain">
                            </div>
                            <h3 class="text-xl font-bold mb-2">Puma</h3>
                            <p class="text-gray-600 text-sm mb-4">Sports lifestyle brand offering innovative designs
                                for both athletic and casual wear.</p>
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-500">180+ Products</span>
                                <a href="#" class="text-black hover:underline text-sm font-medium">View
                                    Collection →</a>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- New Arrivals / Best Sellers Section -->
            <section class="mb-12" x-data="{ activeTab: 'new' }">
                <!-- Section Header -->
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-semibold text-black">Our Products</h2>
                    <a href="#" class="text-black hover:underline font-medium">View All</a>
                </div>

                <!-- Tab Navigation (Enhanced Styling) -->
                <div class="mb-6 border-b border-gray-200">
                    <button @click="activeTab = 'new'"
                        :class="{ 'border-black font-semibold': activeTab === 'new', 'border-transparent text-gray-500 hover:text-black hover:border-gray-300': activeTab !== 'new' }"
                        class="py-2 px-4 -mb-px border-b-2 focus:outline-none transition-colors duration-200">
                        New Arrivals
                    </button>
                    <button @click="activeTab = 'best'"
                        :class="{ 'border-black font-semibold': activeTab === 'best', 'border-transparent text-gray-500 hover:text-black hover:border-gray-300': activeTab !== 'best' }"
                        class="py-2 px-4 -mb-px border-b-2 focus:outline-none transition-colors duration-200">
                        Best Sellers
                    </button>
                </div>

                <!-- New Arrivals Grid -->
                <div x-show="activeTab === 'new'"
                    class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    <!-- Example Product Cards -->
                    <div
                        class="bg-white border rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1">
                        <div class="relative">
                            <div class="bg-gray-200 h-56 w-full flex items-center justify-center text-gray-500">Image
                                Placeholder</div>
                            <div class="absolute top-2 left-2 bg-black text-white text-xs font-bold px-2 py-1 rounded">
                                NEW</div>
                        </div>
                        <div class="p-4">
                            <p class="text-gray-500 text-sm mb-1">Category</p>
                            <h3 class="font-semibold text-lg mb-1 hover:text-gray-700">Modern Product Name</h3>
                            <p class="text-gray-900 font-bold mb-2">$129.99</p>
                            <button
                                class="w-full bg-black text-white py-2 rounded-md hover:bg-gray-800 transition-colors duration-200 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                                Add to Cart
                            </button>
                        </div>
                    </div>

                    <div
                        class="bg-white border rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1">
                        <div class="relative">
                            <div class="bg-gray-200 h-56 w-full flex items-center justify-center text-gray-500">Image
                                Placeholder</div>
                            <div class="absolute top-2 left-2 bg-black text-white text-xs font-bold px-2 py-1 rounded">
                                NEW</div>
                        </div>
                        <div class="p-4">
                            <p class="text-gray-500 text-sm mb-1">Category</p>
                            <h3 class="font-semibold text-lg mb-1 hover:text-gray-700">Stylish Product</h3>
                            <p class="text-gray-900 font-bold mb-2">$89.99</p>
                            <button
                                class="w-full bg-black text-white py-2 rounded-md hover:bg-gray-800 transition-colors duration-200 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                                Add to Cart
                            </button>
                        </div>
                    </div>

                    <div
                        class="bg-white border rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1">
                        <div class="relative">
                            <div class="bg-gray-200 h-56 w-full flex items-center justify-center text-gray-500">Image
                                Placeholder</div>
                            <div class="absolute top-2 left-2 bg-black text-white text-xs font-bold px-2 py-1 rounded">
                                NEW</div>
                        </div>
                        <div class="p-4">
                            <p class="text-gray-500 text-sm mb-1">Category</p>
                            <h3 class="font-semibold text-lg mb-1 hover:text-gray-700">Premium Product</h3>
                            <p class="text-gray-900 font-bold mb-2">$199.99</p>
                            <button
                                class="w-full bg-black text-white py-2 rounded-md hover:bg-gray-800 transition-colors duration-200 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                                Add to Cart
                            </button>
                        </div>
                    </div>

                    <div
                        class="bg-white border rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1">
                        <div class="relative">
                            <div class="bg-gray-200 h-56 w-full flex items-center justify-center text-gray-500">Image
                                Placeholder</div>
                            <div class="absolute top-2 left-2 bg-black text-white text-xs font-bold px-2 py-1 rounded">
                                NEW</div>
                        </div>
                        <div class="p-4">
                            <p class="text-gray-500 text-sm mb-1">Category</p>
                            <h3 class="font-semibold text-lg mb-1 hover:text-gray-700">Designer Product</h3>
                            <p class="text-gray-900 font-bold mb-2">$149.99</p>
                            <button
                                class="w-full bg-black text-white py-2 rounded-md hover:bg-gray-800 transition-colors duration-200 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                                Add to Cart
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Best Sellers Grid -->
                <div x-show="activeTab === 'best'"
                    class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    <!-- Example Product Cards for Best Sellers -->
                    <div
                        class="bg-white border rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1">
                        <div class="relative">
                            <div class="bg-gray-200 h-56 w-full flex items-center justify-center text-gray-500">Image
                                Placeholder</div>
                            <div class="absolute top-2 left-2 bg-black text-white text-xs font-bold px-2 py-1 rounded">
                                BEST SELLER</div>
                        </div>
                        <div class="p-4">
                            <p class="text-gray-500 text-sm mb-1">Category</p>
                            <h3 class="font-semibold text-lg mb-1 hover:text-gray-700">Popular Product</h3>
                            <div class="flex items-center mb-2">
                                <p class="text-gray-900 font-bold mr-2">$79.99</p>
                                <p class="text-gray-500 text-sm line-through">$99.99</p>
                            </div>
                            <button
                                class="w-full bg-black text-white py-2 rounded-md hover:bg-gray-800 transition-colors duration-200 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                                Add to Cart
                            </button>
                        </div>
                    </div>

                    <div
                        class="bg-white border rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1">
                        <div class="relative">
                            <div class="bg-gray-200 h-56 w-full flex items-center justify-center text-gray-500">Image
                                Placeholder</div>
                            <div class="absolute top-2 left-2 bg-black text-white text-xs font-bold px-2 py-1 rounded">
                                BEST SELLER</div>
                        </div>
                        <div class="p-4">
                            <p class="text-gray-500 text-sm mb-1">Category</p>
                            <h3 class="font-semibold text-lg mb-1 hover:text-gray-700">Top Rated Product</h3>
                            <div class="flex items-center mb-2">
                                <p class="text-gray-900 font-bold mr-2">$119.99</p>
                                <p class="text-gray-500 text-sm line-through">$149.99</p>
                            </div>
                            <button
                                class="w-full bg-black text-white py-2 rounded-md hover:bg-gray-800 transition-colors duration-200 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                                Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Newsletter Section -->
            <section class="mb-12 bg-gray-100 p-8 rounded-lg">
                <div class="max-w-2xl mx-auto text-center">
                    <h2 class="text-2xl font-semibold mb-3">Join Our Newsletter</h2>
                    <p class="text-gray-600 mb-6">Stay updated with the latest products, exclusive offers, and fashion
                        news.</p>
                    <div class="flex flex-col sm:flex-row gap-2">
                        <input type="email" placeholder="Your email address"
                            class="flex-grow p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent">
                        <button
                            class="bg-black text-white font-semibold py-3 px-6 rounded-md hover:bg-gray-800 transition-colors duration-200">Subscribe</button>
                    </div>
                </div>
            </section>

        </main>

    </div>

    <!-- Footer -->
    <footer class="bg-black text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">Brandify</h3>
                    <p class="text-gray-400 mb-4">Your destination for premium brands and designer products.</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200">
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M22.675 0h-21.35c-.732 0-1.325.593-1.325 1.325v21.351c0 .731.593 1.324 1.325 1.324h11.495v-9.294h-3.128v-3.622h3.128v-2.671c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.795.143v3.24l-1.918.001c-1.504 0-1.795.715-1.795 1.763v2.313h3.587l-.467 3.622h-3.12v9.293h6.116c.73 0 1.323-.593 1.323-1.325v-21.35c0-.732-.593-1.325-1.325-1.325z" />
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200">
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200">
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
                            </svg>
                        </a>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Shop</h3>
                    <ul class="space-y-2">
                        <li><a href="#"
                                class="text-gray-400 hover:text-white transition-colors duration-200">New Arrivals</a>
                        </li>
                        <li><a href="#"
                                class="text-gray-400 hover:text-white transition-colors duration-200">Best Sellers</a>
                        </li>
                        <li><a href="#"
                                class="text-gray-400 hover:text-white transition-colors duration-200">Brands</a></li>
                        <li><a href="#"
                                class="text-gray-400 hover:text-white transition-colors duration-200">Sale</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Help</h3>
                    <ul class="space-y-2">
                        <li><a href="#"
                                class="text-gray-400 hover:text-white transition-colors duration-200">Contact Us</a>
                        </li>
                        <li><a href="#"
                                class="text-gray-400 hover:text-white transition-colors duration-200">FAQs</a></li>
                        <li><a href="#"
                                class="text-gray-400 hover:text-white transition-colors duration-200">Shipping</a></li>
                        <li><a href="#"
                                class="text-gray-400 hover:text-white transition-colors duration-200">Returns</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">About</h3>
                    <ul class="space-y-2">
                        <li><a href="#"
                                class="text-gray-400 hover:text-white transition-colors duration-200">Our Story</a>
                        </li>
                        <li><a href="#"
                                class="text-gray-400 hover:text-white transition-colors duration-200">Careers</a></li>
                        <li><a href="#"
                                class="text-gray-400 hover:text-white transition-colors duration-200">Privacy
                                Policy</a></li>
                        <li><a href="#"
                                class="text-gray-400 hover:text-white transition-colors duration-200">Terms of
                                Service</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; {{ date('Y') }} Brandify. All rights reserved.</p>
            </div>
        </div>
    </footer>

            <!-- Hero Slider JavaScript -->
            document.addEventListener('DOMContentLoaded', () => {
                // Hero Slider
                const slider = document.getElementById('hero-slider');
                if (!slider) return;
                
                const slides = slider.querySelectorAll('[data-slide]');
                const prevBtn = document.getElementById('slider-prev');
                const nextBtn = document.getElementById('slider-next');
                const dots = document.querySelectorAll('.slider-dot');
                let currentSlide = 0;
                let slideInterval;
                let isTransitioning = false;
                const TRANSITION_DURATION = 1000; // Match this with CSS transition duration
                
                // Show slide function with smooth transitions
                function showSlide(index) {
                    if (isTransitioning) return;
                    isTransitioning = true;
                    
                    // Update current slide index
                    currentSlide = (index + slides.length) % slides.length;
                    
                    // Hide all slides
                    slides.forEach((slide, i) => {
                        slide.style.opacity = '0';
                        slide.style.zIndex = '1';
                        slide.style.pointerEvents = 'none';
                    });
                    
                    // Show current slide
                    const currentSlideEl = slides[currentSlide];
                    currentSlideEl.style.opacity = '1';
                    currentSlideEl.style.zIndex = '2';
                    currentSlideEl.style.pointerEvents = 'auto';
                    
                    // Update active dot
                    dots.forEach((dot, i) => {
                        if (i === currentSlide) {
                            dot.classList.add('active');
                            dot.classList.add('bg-opacity-100');
                            dot.classList.add('w-8');
                        } else {
                            dot.classList.remove('active');
                            dot.classList.remove('bg-opacity-100');
                            dot.classList.remove('w-8');
                        }
                    });
                    
                    // Reset transitioning flag after animation completes
                    setTimeout(() => {
                        isTransitioning = false;
                    }, TRANSITION_DURATION);
                }
                
                // Next slide function
                function nextSlide() {
                    showSlide(currentSlide + 1);
                }
                
                // Previous slide function
                function prevSlide() {
                    showSlide(currentSlide - 1);
                }
                
                // Start autoplay
                function startAutoplay() {
                    stopAutoplay(); // Clear any existing interval
                    slideInterval = setInterval(nextSlide, 5000);
                }
                
                // Stop autoplay
                function stopAutoplay() {
                    if (slideInterval) {
                        clearInterval(slideInterval);
                    }
                }
                
                // Event listeners for navigation
                if (nextBtn) {
                    nextBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        nextSlide();
                        stopAutoplay();
                        startAutoplay();
                    });
                }
                
                if (prevBtn) {
                    prevBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        prevSlide();
                        stopAutoplay();
                        startAutoplay();
                    });
                }
                
                // Dot navigation
                dots.forEach((dot, index) => {
                    dot.addEventListener('click', (e) => {
                        e.preventDefault();
                        if (index === currentSlide) return;
                        showSlide(index);
                        stopAutoplay();
                        startAutoplay();
                    });
                });
                
                // Pause on hover
                slider.addEventListener('mouseenter', stopAutoplay);
                slider.addEventListener('mouseleave', startAutoplay);
                
                // Handle window resize
                let resizeTimer;
                window.addEventListener('resize', () => {
                    clearTimeout(resizeTimer);
                    resizeTimer = setTimeout(() => {
                        // Force a reflow to ensure smooth transitions after resize
                        slider.style.display = 'none';
                        slider.offsetHeight; // Trigger reflow
                        slider.style.display = '';
                    }, 250);
                });
                
                // Initialize slider
                showSlide(0);
                startAutoplay();
                
                // Add keyboard navigation
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'ArrowRight') {
                        nextSlide();
                        stopAutoplay();
                        startAutoplay();
                    } else if (e.key === 'ArrowLeft') {
                        prevSlide();
                        stopAutoplay();
                        startAutoplay();
                    }
                });
            });
        });
    </script>
</body>

</html>
