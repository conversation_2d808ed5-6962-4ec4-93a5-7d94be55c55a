<?php

/**
 * Comprehensive Test Script
 * Tests all the fixes applied to ensure everything works
 */

require_once 'vendor/autoload.php';

echo "🧪 Starting comprehensive tests...\n\n";

$errors = [];
$successes = [];

// Test 1: Check if Brand model is removed
echo "1️⃣ Testing Brand model removal...\n";
if (!file_exists('app/Models/Brand.php')) {
    $successes[] = "✅ Brand model successfully removed";
} else {
    $errors[] = "❌ Brand model still exists";
}

// Test 2: Check if brand views are removed
echo "2️⃣ Testing brand views removal...\n";
if (!file_exists('resources/views/admin/brands/index.blade.php')) {
    $successes[] = "✅ Brand views successfully removed";
} else {
    $errors[] = "❌ Brand views still exist";
}

// Test 3: Check currency configuration
echo "3️⃣ Testing currency configuration...\n";
if (file_exists('config/brandify.php')) {
    $config = include 'config/brandify.php';
    if (isset($config['currency']['symbol']) && $config['currency']['symbol'] === '₦') {
        $successes[] = "✅ Currency symbol correctly configured";
    } else {
        $errors[] = "❌ Currency symbol not properly configured";
    }
} else {
    $errors[] = "❌ Brandify config file missing";
}

// Test 4: Check if earnings view uses @currency directive
echo "4️⃣ Testing earnings view currency fixes...\n";
if (file_exists('resources/views/vendor/earnings/index.blade.php')) {
    $content = file_get_contents('resources/views/vendor/earnings/index.blade.php');
    if (strpos($content, '@currency(') !== false && strpos($content, '${{') === false) {
        $successes[] = "✅ Earnings view uses @currency directive";
    } else {
        $errors[] = "❌ Earnings view still has currency issues";
    }
} else {
    $errors[] = "❌ Earnings view file missing";
}

// Test 5: Check vendor settings controller
echo "5️⃣ Testing vendor settings controller...\n";
if (file_exists('app/Http/Controllers/Vendor/SettingsController.php')) {
    $successes[] = "✅ Vendor settings controller exists";
} else {
    $errors[] = "❌ Vendor settings controller missing";
}

// Test 6: Check vendor settings view
echo "6️⃣ Testing vendor settings view...\n";
if (file_exists('resources/views/vendor/settings/index.blade.php')) {
    $successes[] = "✅ Vendor settings view exists";
} else {
    $errors[] = "❌ Vendor settings view missing";
}

// Test 7: Check Product model for brand references
echo "7️⃣ Testing Product model for brand references...\n";
if (file_exists('app/Models/Product.php')) {
    $content = file_get_contents('app/Models/Product.php');
    if (strpos($content, 'brand()') === false && strpos($content, 'brand_id') === false) {
        $successes[] = "✅ Product model has no brand references";
    } else {
        $errors[] = "❌ Product model still has brand references";
    }
} else {
    $errors[] = "❌ Product model missing";
}

// Test 8: Check migration file
echo "8️⃣ Testing comprehensive migration...\n";
if (file_exists('database/migrations/2025_01_20_000000_comprehensive_brand_cleanup.php')) {
    $successes[] = "✅ Comprehensive migration file exists";
} else {
    $errors[] = "❌ Comprehensive migration file missing";
}

// Test 9: Check JavaScript files for currency fixes
echo "9️⃣ Testing JavaScript currency fixes...\n";
if (file_exists('public/js/search.js')) {
    $content = file_get_contents('public/js/search.js');
    if (strpos($content, '₦') !== false && strpos($content, '$') === false) {
        $successes[] = "✅ Search.js uses correct currency symbol";
    } else {
        $errors[] = "❌ Search.js has currency issues";
    }
} else {
    $errors[] = "❌ Search.js file missing";
}

// Test 10: Check subscription system
echo "🔟 Testing subscription system...\n";
if (file_exists('app/Http/Controllers/Vendor/SubscriptionController.php')) {
    $content = file_get_contents('app/Http/Controllers/Vendor/SubscriptionController.php');
    if (strpos($content, 'initializePaystackPayment') !== false) {
        $successes[] = "✅ Subscription system has Paystack integration";
    } else {
        $errors[] = "❌ Subscription system missing Paystack integration";
    }
} else {
    $errors[] = "❌ Subscription controller missing";
}

// Display results
echo "\n📊 TEST RESULTS:\n";
echo "================\n";

echo "\n✅ SUCCESSES (" . count($successes) . "):\n";
foreach ($successes as $success) {
    echo $success . "\n";
}

if (!empty($errors)) {
    echo "\n❌ ERRORS (" . count($errors) . "):\n";
    foreach ($errors as $error) {
        echo $error . "\n";
    }
} else {
    echo "\n🎉 ALL TESTS PASSED! No errors found.\n";
}

// Overall status
$totalTests = count($successes) + count($errors);
$successRate = (count($successes) / $totalTests) * 100;

echo "\n📈 OVERALL STATUS:\n";
echo "Success Rate: " . round($successRate, 1) . "% (" . count($successes) . "/" . $totalTests . ")\n";

if ($successRate >= 90) {
    echo "🟢 EXCELLENT: Application is ready for testing!\n";
} elseif ($successRate >= 70) {
    echo "🟡 GOOD: Minor issues remain, but mostly functional.\n";
} else {
    echo "🔴 NEEDS WORK: Significant issues need to be addressed.\n";
}

echo "\n🚀 READY FOR MANUAL TESTING:\n";
echo "1. Visit: http://localhost:8000/vendor/dashboard\n";
echo "2. Visit: http://localhost:8000/vendor/products\n";
echo "3. Visit: http://localhost:8000/vendor/earnings\n";
echo "4. Visit: http://localhost:8000/vendor/settings\n";
echo "5. Visit: http://localhost:8000/shop\n";
echo "6. Test search functionality\n";
echo "7. Test product creation/editing\n";

?>
