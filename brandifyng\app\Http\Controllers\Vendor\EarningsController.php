<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Commission;
use App\Models\Withdrawal;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EarningsController extends Controller
{
    /**
     * Display the vendor's earnings and commissions.
     */
    public function index(Request $request)
    {
    
        $vendor = auth()->user()->vendor;
        $period = $request->input('period', 'month'); // Default to monthly view
        
        // Default date range (current month)
        $startDate = Carbon::now()->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();
        
        // If period is set, adjust date range
        if ($period === 'week') {
            $startDate = Carbon::now()->startOfWeek();
            $endDate = Carbon::now()->endOfWeek();
        } elseif ($period === 'year') {
            $startDate = Carbon::now()->startOfYear();
            $endDate = Carbon::now()->endOfYear();
        } elseif ($period === 'all') {
            $startDate = Carbon::createFromTimestamp(0); // Beginning of time
            $endDate = Carbon::now(); // Now
        } elseif ($period === 'custom' && $request->filled('start_date') && $request->filled('end_date')) {
            $startDate = Carbon::parse($request->input('start_date'))->startOfDay();
            $endDate = Carbon::parse($request->input('end_date'))->endOfDay();
        }
        
        // Get sales data
        $salesData = $vendor->products()
            ->join('order_items', 'products.id', '=', 'order_items.product_id')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->where('orders.status', '!=', 'cancelled')
            ->select(
                DB::raw('DATE(orders.created_at) as date'),
                DB::raw('SUM(order_items.price_at_purchase * order_items.quantity) as total_sales')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();
        
        // Get total sales in the period
        $totalSales = $salesData->sum('total_sales');
        
        // Get commissions
        $commissions = Commission::where('vendor_id', $vendor->id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->orderBy('created_at', 'desc')
            ->paginate(10);
        
        // Get paid and pending commissions
        $paidCommissions = Commission::where('vendor_id', $vendor->id)
            ->where('status', 'paid')
            ->sum('amount');
            
        $pendingCommissions = Commission::where('vendor_id', $vendor->id)
            ->where('status', 'pending')
            ->sum('amount');
        
        // Calculate earnings (sales minus commissions)
        $totalCommissions = $commissions->sum('amount');
        $totalEarnings = $totalSales - $totalCommissions;
        
        // Format data for the chart
        $chartDates = $salesData->pluck('date');
        $chartSales = $salesData->pluck('total_sales');
        
        return view('vendor.earnings.index', compact(
            'period', 'totalSales', 'totalCommissions', 'totalEarnings',
            'paidCommissions', 'pendingCommissions', 'commissions',
            'chartDates', 'chartSales', 'startDate', 'endDate'
        ));
    }
    
    /**
     * Request a withdrawal of available funds.
     */
    public function withdraw(Request $request)
    {
        $vendor = auth()->user()->vendor;
        
        // Validate the request
        $request->validate([
            'amount' => 'required|numeric|min:1',
            'withdrawal_method' => 'required|in:bank_transfer,paystack,paypal',
            'bank_name' => 'required_if:withdrawal_method,bank_transfer',
            'account_name' => 'required_if:withdrawal_method,bank_transfer',
            'account_number' => 'required_if:withdrawal_method,bank_transfer',
            'paystack_email' => 'required_if:withdrawal_method,paystack|email',
            'paypal_email' => 'required_if:withdrawal_method,paypal|email',
        ]);
        
        // Check if the requested amount is available
        $pendingCommissions = Commission::where('vendor_id', $vendor->id)
            ->where('status', 'pending')
            ->sum('amount');
            
        if ($request->amount > $pendingCommissions) {
            return redirect()->route('vendor.earnings.index')
                ->with('error', 'Withdrawal amount exceeds available funds.');
        }
        
        // Store details based on withdrawal method
        $details = [];
        if ($request->withdrawal_method === 'bank_transfer') {
            $details = [
                'bank_name' => $request->bank_name,
                'account_name' => $request->account_name,
                'account_number' => $request->account_number
            ];
        } elseif ($request->withdrawal_method === 'paystack') {
            $details = [
                'email' => $request->paystack_email
            ];
        } elseif ($request->withdrawal_method === 'paypal') {
            $details = [
                'email' => $request->paypal_email
            ];
        }
        
        // Create withdrawal request
        $withdrawal = Withdrawal::create([
            'vendor_id' => $vendor->id,
            'amount' => $request->amount,
            'method' => $request->withdrawal_method,
            'details' => json_encode($details),
            'status' => 'pending'
        ]);
        
        return redirect()->route('vendor.earnings.index')
            ->with('success', 'Your withdrawal request has been submitted successfully. We will process it within 1-3 business days.');
    }
}
