<?php

namespace App\Services;

use App\Models\Order;
use App\Models\Vendor;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

class ShipBubbleService
{
    protected $apiKey;
    protected $apiUrl;
    protected $timeout;

    public function __construct()
    {
        $this->apiKey = config('services.shipbubble.api_key') ?? env('SHIPBUBBLE_API_KEY');
        $this->apiUrl = config('services.shipbubble.base_url', 'https://api.shipbubble.com/v1');
        $this->timeout = config('ecommerce.shipping.shipbubbles.timeout', 30);
    }

    /**
     * Create a shipment for an order
     *
     * @param Order $order
     * @return array|null
     */
    public function createShipment(Order $order): ?array
    {
        try {
            if (!$this->isEnabled()) {
                Log::info('ShipBubbles integration is disabled');
                return null;
            }

            if (!$this->apiKey) {
                throw new Exception('ShipBubbles API key not configured');
            }

            // Get platform delivery items only
            $platformDeliveryItems = $order->items()
                ->whereHas('product', function ($query) {
                    $query->where('requires_platform_delivery', true);
                })
                ->with(['product', 'vendor'])
                ->get();

            if ($platformDeliveryItems->isEmpty()) {
                Log::info("No platform delivery items found for order {$order->order_number}");
                return null;
            }

            // Group items by vendor for multiple shipments if needed
            $itemsByVendor = $platformDeliveryItems->groupBy('vendor_id');
            $shipments = [];

            foreach ($itemsByVendor as $vendorId => $items) {
                $vendor = $items->first()->vendor;

                // Step 1: Get or create addresses
                $senderAddressCode = $this->getOrCreateAddress($vendor, 'sender');
                $receiverAddressCode = $this->getOrCreateAddress($order, 'receiver');

                if (!$senderAddressCode || !$receiverAddressCode) {
                    Log::error("Failed to create addresses for shipment", [
                        'vendor_id' => $vendorId,
                        'order_id' => $order->id
                    ]);
                    continue;
                }

                // Step 2: Get shipping rates
                $ratesData = $this->getShippingRates($senderAddressCode, $receiverAddressCode, $items);

                if (!$ratesData || !isset($ratesData['request_token'])) {
                    Log::error("Failed to get shipping rates", [
                        'vendor_id' => $vendorId,
                        'order_id' => $order->id
                    ]);
                    continue;
                }

                // Step 3: Create shipment using the cheapest courier
                $cheapestCourier = $ratesData['cheapest_courier'] ?? $ratesData['couriers'][0] ?? null;

                if (!$cheapestCourier) {
                    Log::error("No courier available for shipment", [
                        'vendor_id' => $vendorId,
                        'order_id' => $order->id
                    ]);
                    continue;
                }

                $shipmentResponse = $this->createShipmentLabel(
                    $ratesData['request_token'],
                    $cheapestCourier['service_code'],
                    $cheapestCourier['courier_id']
                );

                if ($shipmentResponse && isset($shipmentResponse['data'])) {
                    $shipments[] = $shipmentResponse['data'];

                    // Update order with shipment ID (for single vendor orders)
                    if ($itemsByVendor->count() === 1) {
                        $order->update([
                            'shipbubble_shipment_id' => $shipmentResponse['data']['order_id'] ?? null,
                            'status' => 'awaiting_platform_pickup'
                        ]);
                    }

                    Log::info("Shipment created successfully", [
                        'vendor_id' => $vendorId,
                        'order_number' => $order->order_number,
                        'shipment_id' => $shipmentResponse['data']['order_id'] ?? null
                    ]);
                } else {
                    Log::error("Failed to create shipment label", [
                        'vendor_id' => $vendorId,
                        'order_id' => $order->id
                    ]);
                }
            }

            return $shipments;

        } catch (Exception $e) {
            Log::error('ShipBubbles shipment creation failed: ' . $e->getMessage(), [
                'order_id' => $order->id,
                'order_number' => $order->order_number
            ]);
            return null;
        }
    }

    /**
     * Get or create address in ShipBubbles system
     *
     * @param mixed $entity (Order or Vendor)
     * @param string $type ('sender' or 'receiver')
     * @return int|null Address code
     */
    protected function getOrCreateAddress($entity, string $type): ?int
    {
        try {
            $addressData = $this->prepareAddressData($entity, $type);

            // Try to validate/create address
            $response = $this->makeApiCall('/shipping/address/validate', 'POST', $addressData);

            if ($response && isset($response['data']['address_code'])) {
                return $response['data']['address_code'];
            }

            Log::error("Failed to create address", [
                'type' => $type,
                'response' => $response
            ]);

            return null;

        } catch (Exception $e) {
            Log::error("Address creation failed: " . $e->getMessage(), [
                'type' => $type
            ]);
            return null;
        }
    }

    /**
     * Prepare address data for ShipBubbles API
     *
     * @param mixed $entity
     * @param string $type
     * @return array
     */
    protected function prepareAddressData($entity, string $type): array
    {
        if ($type === 'sender' && $entity instanceof Vendor) {
            return [
                'name' => $entity->business_name ?? $entity->shop_name,
                'phone' => $entity->contact_phone ?? $entity->user->phone ?? '',
                'email' => $entity->contact_email_business ?? $entity->user->email,
                'address' => $entity->address_line1 ?? $entity->address,
                'city' => $entity->city,
                'state' => $entity->state,
                'country' => $entity->country ?? 'Nigeria'
            ];
        } elseif ($type === 'receiver' && $entity instanceof Order) {
            return [
                'name' => $entity->shipping_name,
                'phone' => $entity->shipping_phone,
                'email' => $entity->user->email,
                'address' => $entity->shipping_address,
                'city' => $entity->shipping_city,
                'state' => $entity->shipping_state,
                'country' => $entity->shipping_country ?? 'Nigeria'
            ];
        }

        throw new Exception("Invalid entity type for address preparation");
    }

    /**
     * Get shipping rates for the shipment
     *
     * @param int $senderAddressCode
     * @param int $receiverAddressCode
     * @param \Illuminate\Support\Collection $items
     * @return array|null
     */
    protected function getShippingRates(int $senderAddressCode, int $receiverAddressCode, $items): ?array
    {
        try {
            $totalValue = $items->sum(function ($item) {
                return $item->price_at_purchase * $item->quantity;
            });

            $totalWeight = $items->sum(function ($item) {
                return ($item->product->weight ?? config('ecommerce.shipping.default_weight', 1.0)) * $item->quantity;
            });

            $packageItems = $items->map(function ($item) {
                return [
                    'name' => $item->product_name,
                    'description' => $item->product->description ?? 'E-commerce item',
                    'unit_weight' => $item->product->weight ?? config('ecommerce.shipping.default_weight', 1.0),
                    'unit_amount' => $item->price_at_purchase,
                    'quantity' => $item->quantity
                ];
            })->toArray();

            $ratesData = [
                'sender_address_code' => $senderAddressCode,
                'reciever_address_code' => $receiverAddressCode, // Note: API uses 'reciever' (typo in their API)
                'pickup_date' => now()->addDay()->format('Y-m-d'), // Next day pickup
                'category_id' => 1, // General category
                'package_items' => $packageItems,
                'service_type' => 'pickup',
                'package_dimension' => [
                    'length' => config('ecommerce.shipping.default_dimensions.length', 20),
                    'width' => config('ecommerce.shipping.default_dimensions.width', 15),
                    'height' => config('ecommerce.shipping.default_dimensions.height', 10)
                ]
            ];

            return $this->makeApiCall('/shipping/fetch_rates', 'POST', $ratesData);

        } catch (Exception $e) {
            Log::error('ShipBubbles rates request failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create shipment label
     *
     * @param string $requestToken
     * @param string $serviceCode
     * @param string $courierId
     * @return array|null
     */
    protected function createShipmentLabel(string $requestToken, string $serviceCode, string $courierId): ?array
    {
        try {
            $labelData = [
                'request_token' => $requestToken,
                'service_code' => $serviceCode,
                'courier_id' => $courierId
            ];

            return $this->makeApiCall('/shipping/labels', 'POST', $labelData);

        } catch (Exception $e) {
            Log::error('ShipBubbles label creation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Track a shipment
     *
     * @param string $shipmentId
     * @return array|null
     */
    public function trackShipment(string $shipmentId): ?array
    {
        try {
            // Get all shipments and filter by order_id
            $response = $this->makeApiCall('/shipping/labels', 'GET');

            if ($response && isset($response['data']['results'])) {
                foreach ($response['data']['results'] as $shipment) {
                    if ($shipment['order_id'] === $shipmentId) {
                        return $shipment;
                    }
                }
            }

            return null;
        } catch (Exception $e) {
            Log::error('ShipBubbles tracking failed: ' . $e->getMessage(), [
                'shipment_id' => $shipmentId
            ]);
            return null;
        }
    }

    /**
     * Make API call to ShipBubbles
     *
     * @param string $endpoint
     * @param string $method
     * @param array $data
     * @return array|null
     */
    protected function makeApiCall(string $endpoint, string $method = 'GET', array $data = []): ?array
    {
        try {
            $response = Http::timeout($this->timeout)
                ->withHeaders([
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json'
                ]);

            if ($method === 'GET') {
                $response = $response->get($this->apiUrl . $endpoint, $data);
            } else {
                $response = $response->$method($this->apiUrl . $endpoint, $data);
            }

            if ($response->successful()) {
                $responseData = $response->json();

                // Log successful API calls for debugging
                Log::info('ShipBubbles API call successful', [
                    'endpoint' => $endpoint,
                    'method' => $method,
                    'status' => $response->status()
                ]);

                return $responseData;
            }

            Log::error('ShipBubbles API call failed', [
                'endpoint' => $endpoint,
                'method' => $method,
                'status' => $response->status(),
                'response' => $response->body(),
                'data' => $data
            ]);

            return null;

        } catch (Exception $e) {
            Log::error('ShipBubbles API call exception', [
                'endpoint' => $endpoint,
                'method' => $method,
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            return null;
        }
    }

    /**
     * Check if ShipBubbles integration is enabled
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return config('services.shipbubble.enabled', true) && !empty($this->apiKey);
    }

    /**
     * Get all shipments for tracking
     *
     * @return array|null
     */
    public function getAllShipments(): ?array
    {
        try {
            return $this->makeApiCall('/shipping/labels', 'GET');
        } catch (Exception $e) {
            Log::error('ShipBubbles get all shipments failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Cancel a shipment
     *
     * @param string $shipmentId
     * @return bool
     */
    public function cancelShipment(string $shipmentId): bool
    {
        try {
            $response = $this->makeApiCall("/shipping/labels/{$shipmentId}/cancel", 'POST');
            return $response && isset($response['status']) && $response['status'] === 'success';
        } catch (Exception $e) {
            Log::error('ShipBubbles cancel shipment failed: ' . $e->getMessage(), [
                'shipment_id' => $shipmentId
            ]);
            return false;
        }
    }

    /**
     * Get package categories
     *
     * @return array|null
     */
    public function getPackageCategories(): ?array
    {
        try {
            return $this->makeApiCall('/shipping/categories', 'GET');
        } catch (Exception $e) {
            Log::error('ShipBubbles get categories failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Validate if the service is properly configured
     *
     * @return bool
     */
    public function validateConfiguration(): bool
    {
        if (!$this->isEnabled()) {
            return false;
        }

        try {
            // Test API connection by getting categories
            $response = $this->getPackageCategories();
            return $response !== null;
        } catch (Exception $e) {
            Log::error('ShipBubbles configuration validation failed: ' . $e->getMessage());
            return false;
        }
    }
}
