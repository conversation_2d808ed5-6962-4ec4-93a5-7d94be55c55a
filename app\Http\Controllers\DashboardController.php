<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Redirect users to the appropriate dashboard based on their role.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        
        // Check user role and redirect accordingly
        if ($user->isAdmin()) {
            return redirect()->route('admin.dashboard');
        } elseif ($user->isVendor()) {
            // If vendor account is pending approval, redirect to onboarding page
            if ($user->vendor && !$user->vendor->is_approved) {
                return redirect()->route('vendor.onboarding')->with('info', 'Your vendor application is pending approval.');
            }
            
            // If vendor account is approved, redirect to vendor dashboard
            return redirect()->route('vendor.dashboard');
        }
        
        // For regular users, show the regular dashboard
        return view('dashboard');
    }
}
