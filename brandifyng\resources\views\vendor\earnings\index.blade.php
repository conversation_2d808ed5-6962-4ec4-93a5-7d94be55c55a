@extends('layouts.vendor')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Earnings & Withdrawals</h1>
    </div>

    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Filter and Date Range -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body p-3">
            <form action="{{ route('vendor.earnings.index') }}" method="GET" class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label for="period" class="form-label">Period</label>
                    <select class="form-select" id="period" name="period" onchange="toggleCustomDate()">
                        <option value="week" {{ $period == 'week' ? 'selected' : '' }}>This Week</option>
                        <option value="month" {{ $period == 'month' ? 'selected' : '' }}>This Month</option>
                        <option value="year" {{ $period == 'year' ? 'selected' : '' }}>This Year</option>
                        <option value="all" {{ $period == 'all' ? 'selected' : '' }}>All Time</option>
                        <option value="custom" {{ $period == 'custom' ? 'selected' : '' }}>Custom Date Range</option>
                    </select>
                </div>
                
                <div class="col-md-3 custom-date {{ $period == 'custom' ? '' : 'd-none' }}">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ $startDate->format('Y-m-d') }}">
                </div>
                
                <div class="col-md-3 custom-date {{ $period == 'custom' ? '' : 'd-none' }}">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ $endDate->format('Y-m-d') }}">
                </div>
                
                <div class="col-md-3">
                    <button type="submit" class="btn btn-dark px-4">Apply Filter</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-4 mb-md-0">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between mb-3">
                        <div>
                            <h6 class="text-muted mb-1">Total Sales</h6>
                            <h3 class="mb-0">${{ number_format($totalSales, 2) }}</h3>
                        </div>
                        <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                            <i class="fas fa-chart-line text-dark"></i>
                        </div>
                    </div>
                    <p class="text-muted mb-0">{{ $startDate->format('M d, Y') }} - {{ $endDate->format('M d, Y') }}</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-4 mb-md-0">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between mb-3">
                        <div>
                            <h6 class="text-muted mb-1">Platform Commission</h6>
                            <h3 class="mb-0">${{ number_format($totalCommissions, 2) }}</h3>
                        </div>
                        <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                            <i class="fas fa-percentage text-dark"></i>
                        </div>
                    </div>
                    <p class="text-muted mb-0">Platform fee for all sales</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-4 mb-md-0">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between mb-3">
                        <div>
                            <h6 class="text-muted mb-1">Your Earnings</h6>
                            <h3 class="mb-0">${{ number_format($totalEarnings, 2) }}</h3>
                        </div>
                        <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                            <i class="fas fa-wallet text-dark"></i>
                        </div>
                    </div>
                    <p class="text-muted mb-0">Sales minus commission</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center justify-content-between mb-3">
                        <div>
                            <h6 class="text-muted mb-1">Available for Withdrawal</h6>
                            <h3 class="mb-0">${{ number_format($pendingCommissions, 2) }}</h3>
                        </div>
                        <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                            <i class="fas fa-money-bill-wave text-dark"></i>
                        </div>
                    </div>
                    <p class="text-muted mb-3">Amount you can withdraw now</p>
                    
                    <div class="mt-auto">
                        <button class="btn btn-dark w-100" data-bs-toggle="modal" data-bs-target="#withdrawModal" {{ $pendingCommissions <= 0 ? 'disabled' : '' }}>
                            <i class="fas fa-credit-card me-2"></i> Withdraw Funds
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sales Chart -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-white py-3">
            <h5 class="fw-bold mb-0">Sales Overview</h5>
        </div>
        <div class="card-body">
            <canvas id="salesChart" height="300"></canvas>
        </div>
    </div>

    <!-- Earnings History -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
            <h5 class="fw-bold mb-0">Earnings History</h5>
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-dark dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-download me-1"></i> Export
                </button>
                <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                    <li><a class="dropdown-item" href="#">CSV</a></li>
                    <li><a class="dropdown-item" href="#">PDF</a></li>
                    <li><a class="dropdown-item" href="#">Excel</a></li>
                </ul>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="bg-light">
                        <tr>
                            <th>Transaction ID</th>
                            <th>Date</th>
                            <th>Sale Amount</th>
                            <th>Commission</th>
                            <th>Your Earnings</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($commissions as $commission)
                            <tr>
                                <td>{{ $commission->id }}</td>
                                <td>{{ $commission->created_at->format('M d, Y') }}</td>
                                <td>${{ number_format($commission->order_amount, 2) }}</td>
                                <td>${{ number_format($commission->amount, 2) }}</td>
                                <td>${{ number_format($commission->order_amount - $commission->amount, 2) }}</td>
                                <td>
                                    @if ($commission->status == 'paid')
                                        <span class="badge bg-success">Paid</span>
                                    @elseif ($commission->status == 'pending')
                                        <span class="badge bg-warning text-dark">Pending</span>
                                    @else
                                        <span class="badge bg-secondary">{{ ucfirst($commission->status) }}</span>
                                    @endif
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                                        <h5 class="fw-bold mb-2">No Earnings Yet</h5>
                                        <p class="text-muted mb-0">You haven't made any sales during this period.</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer bg-white border-0">
            <div class="d-flex justify-content-center">
                {{ $commissions->links() }}
            </div>
        </div>
    </div>
</div>

<!-- Withdraw Modal -->
<div class="modal fade" id="withdrawModal" tabindex="-1" aria-labelledby="withdrawModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="withdrawModalLabel">Withdraw Funds</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('vendor.earnings.withdraw') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="amount" class="form-label">Amount to Withdraw</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="1" max="{{ $pendingCommissions }}" value="{{ $pendingCommissions }}" required>
                        </div>
                        <small class="text-muted">Maximum available: ${{ number_format($pendingCommissions, 2) }}</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="withdrawal_method" class="form-label">Withdrawal Method</label>
                        <select class="form-select" id="withdrawal_method" name="withdrawal_method" required>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="paystack">Paystack</option>
                            <option value="paypal">PayPal</option>
                        </select>
                    </div>
                    
                    <div id="bank_details" class="mb-3">
                        <label for="bank_name" class="form-label">Bank Name</label>
                        <input type="text" class="form-control mb-2" id="bank_name" name="bank_name">
                        
                        <label for="account_name" class="form-label">Account Name</label>
                        <input type="text" class="form-control mb-2" id="account_name" name="account_name">
                        
                        <label for="account_number" class="form-label">Account Number</label>
                        <input type="text" class="form-control" id="account_number" name="account_number">
                    </div>
                    
                    <div id="paystack_details" class="mb-3 d-none">
                        <label for="paystack_email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="paystack_email" name="paystack_email">
                    </div>
                    
                    <div id="paypal_details" class="mb-3 d-none">
                        <label for="paypal_email" class="form-label">PayPal Email</label>
                        <input type="email" class="form-control" id="paypal_email" name="paypal_email">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-dark">Request Withdrawal</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle custom date fields
        function toggleCustomDate() {
            const periodSelect = document.getElementById('period');
            const customDateFields = document.querySelectorAll('.custom-date');
            
            if (periodSelect.value === 'custom') {
                customDateFields.forEach(field => field.classList.remove('d-none'));
            } else {
                customDateFields.forEach(field => field.classList.add('d-none'));
            }
        }
        
        // Initialize toggle
        window.toggleCustomDate = toggleCustomDate;
        
        // Toggle withdrawal method details
        const withdrawalMethod = document.getElementById('withdrawal_method');
        const bankDetails = document.getElementById('bank_details');
        const paystackDetails = document.getElementById('paystack_details');
        const paypalDetails = document.getElementById('paypal_details');
        
        withdrawalMethod.addEventListener('change', function() {
            bankDetails.classList.add('d-none');
            paystackDetails.classList.add('d-none');
            paypalDetails.classList.add('d-none');
            
            if (this.value === 'bank_transfer') {
                bankDetails.classList.remove('d-none');
            } else if (this.value === 'paystack') {
                paystackDetails.classList.remove('d-none');
            } else if (this.value === 'paypal') {
                paypalDetails.classList.remove('d-none');
            }
        });
        
        // Initialize sales chart
        const ctx = document.getElementById('salesChart').getContext('2d');
        const salesChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: @json($chartDates),
                datasets: [{
                    label: 'Sales',
                    data: @json($chartSales),
                    backgroundColor: 'rgba(0, 0, 0, 0.1)',
                    borderColor: '#000',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(context.raw);
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value;
                            }
                        }
                    }
                }
            }
        });
    });
</script>
@endpush
@endsection
