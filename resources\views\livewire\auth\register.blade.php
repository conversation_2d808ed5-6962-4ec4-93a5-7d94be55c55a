<div class="auth-form">
    <h2 class="text-center mb-4">{{ __('Create an account') }}</h2>
    <p class="text-center text-muted mb-4">{{ __('Enter your details below to create your account') }}</p>

    <!-- Logo -->
    <div class="text-center mb-4">
        <img src="{{ asset('images/brandify-logo.png') }}" alt="Brandify" class="img-fluid" style="max-width: 180px;">
    </div>
    
    <!-- Session Status -->
    @if (session('status'))
        <div class="alert alert-success text-center mb-4">
            {{ session('status') }}
        </div>
    @endif
    
    <!-- Success Message -->
    @if (session('success'))
        <div class="alert alert-success text-center mb-4">
            {{ session('success') }}
        </div>
    @endif
    
    <!-- Error Message -->
    @if (session('error'))
        <div class="alert alert-danger text-center mb-4">
            {{ session('error') }}
        </div>
    @endif

    <form wire:submit="register" class="mb-4">
        <!-- Name -->
        <div class="form-group mb-3">
            <label for="name" class="form-label">{{ __('Name') }}</label>
            <input wire:model="name" id="name" type="text"
                class="form-control @error('name') is-invalid @enderror" required autofocus autocomplete="name"
                placeholder="{{ __('Full name') }}">
            @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- Email Address -->
        <div class="form-group mb-3">
            <label for="email" class="form-label">{{ __('Email address') }}</label>
            <input wire:model="email" id="email" type="email"
                class="form-control @error('email') is-invalid @enderror" required autocomplete="email"
                placeholder="<EMAIL>">
            @error('email')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- Password -->
        <div class="form-group mb-3">
            <label for="password" class="form-label">{{ __('Password') }}</label>
            <input wire:model="password" id="password" type="password"
                class="form-control @error('password') is-invalid @enderror" required autocomplete="new-password"
                placeholder="{{ __('Password') }}">
            @error('password')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- Confirm Password -->
        <div class="form-group mb-3">
            <label for="password_confirmation" class="form-label">{{ __('Confirm password') }}</label>
            <input wire:model="password_confirmation" id="password_confirmation" type="password"
                class="form-control @error('password_confirmation') is-invalid @enderror" required
                autocomplete="new-password" placeholder="{{ __('Confirm password') }}">
            @error('password_confirmation')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <button type="submit" class="btn-primary w-100 mb-3" wire:loading.attr="disabled">
            <span wire:loading.remove wire:target="register">{{ __('Create account') }}</span>
            <span wire:loading wire:target="register">
                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                {{ __('Creating account...') }}
            </span>
        </button>
    </form>

    <div class="text-center text-sm text-muted">
        {{ __('Already have an account?') }}
        <a href="{{ route('login') }}" class="auth-link" wire:navigate>{{ __('Log in') }}</a>
    </div>
</div>
