<div class="auth-form">
    <h2 class="text-center mb-4">{{ __('Forgot password') }}</h2>
    <p class="text-center text-muted mb-4">{{ __('Enter your email to receive a password reset link') }}</p>

    <!-- Session Status -->
    @if (session('status'))
        <div class="alert alert-success text-center mb-4">
            {{ session('status') }}
        </div>
    @endif

    <form wire:submit="sendPasswordResetLink" class="mb-4">
        <!-- Email Address -->
        <div class="form-group mb-3">
            <label for="email" class="form-label">{{ __('Email address') }}</label>
            <input wire:model="email" id="email" type="email"
                class="form-control @error('email') is-invalid @enderror" required autofocus
                placeholder="<EMAIL>">
            @error('email')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <button type="submit" class="btn-primary w-100 mb-3">
            {{ __('Email password reset link') }}
        </button>
    </form>

    <div class="text-center text-sm text-muted">
        {{ __('Or, return to') }}
        <a href="{{ route('login') }}" class="auth-link" wire:navigate>{{ __('log in') }}</a>
    </div>
</div>
