<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
            // Add subscription tracking fields
            $table->integer('orders_processed')->default(0)->after('subscription_status');
            $table->integer('free_order_limit')->default(10)->after('orders_processed');
            $table->timestamp('subscription_started_at')->nullable()->after('free_order_limit');
            $table->timestamp('subscription_expires_at')->nullable()->after('subscription_started_at');
            $table->decimal('monthly_subscription_fee', 8, 2)->default(50.00)->after('subscription_expires_at');
            $table->boolean('subscription_required')->default(false)->after('monthly_subscription_fee');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
            $table->dropColumn([
                'orders_processed',
                'free_order_limit',
                'subscription_started_at',
                'subscription_expires_at',
                'monthly_subscription_fee',
                'subscription_required'
            ]);
        });
    }
};
