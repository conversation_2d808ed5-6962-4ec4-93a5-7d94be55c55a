@extends('layouts.admin')
@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="fw-bold">Order #{{ $order->id }}</h2>
        <a href="{{ route('admin.orders.index') }}" class="btn btn-outline-dark">Back</a>
    </div>
    <div class="card mb-3">
        <div class="card-body">
            <div class="mb-2"><strong>Customer:</strong> {{ $order->user->name ?? '' }} ({{ $order->user->email ?? '' }})</div>
            <div class="mb-2"><strong>Total:</strong> ${{ number_format($order->total, 2) }}</div>
            <div class="mb-2"><strong>Status:</strong> <span class="badge bg-dark">{{ ucfirst($order->status) }}</span></div>
            <div class="mb-2"><strong>Date:</strong> {{ $order->created_at->format('M d, Y') }}</div>
        </div>
    </div>
    <div class="card">
        <div class="card-header fw-bold">Order Items</div>
        <div class="card-body p-0">
            <table class="table table-sm table-borderless mb-0">
                <thead>
                    <tr>
                        <th>Product</th>
                        <th>Quantity</th>
                        <th>Price</th>
                        <th>Subtotal</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($order->items as $item)
                    <tr>
                        <td>{{ $item->product->name ?? '' }}</td>
                        <td>{{ $item->quantity }}</td>
                        <td>${{ number_format($item->price, 2) }}</td>
                        <td>${{ number_format($item->price * $item->quantity, 2) }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection
