@extends('layouts.app')

@section('content')
    <div class="container-fluid py-4 px-3 px-sm-4 px-md-5">
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('home') }}"
                                class="text-decoration-none text-dark">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('products.index') }}"
                                class="text-decoration-none text-dark">Shop</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Shopping Cart</li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <h1 class="fw-bold mb-4">Your Shopping Cart <span class="badge bg-dark ms-2">{{ $cartItems->count() }} {{ Str::plural('item', $cartItems->count()) }}</span></h1>
            </div>
        </div>

        @if (session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        @if (session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        <!-- Cart Items -->
        <div class="row">
            <div class="col-lg-8 mb-4">
                <div class="card border-0 shadow">
                    <div id="cart-items-container" {{ $cartItems->count() === 0 ? 'style="display: none;"' : '' }} data-initial-count="{{ $cartItems->count() }}">
                        @if ($cartItems->count() > 0)
                            <div class="card-body p-0">
                                @foreach ($cartItems as $id => $item)
                                    <div class="d-flex flex-column flex-md-row p-4 {{ !$loop->last ? 'border-bottom' : '' }}">
                                        <div class="flex-shrink-0 text-center text-md-start mb-3 mb-md-0">
                                            @if(isset($item['image_url']) && (filter_var($item['image_url'], FILTER_VALIDATE_URL) || file_exists(public_path($item['image_url']))))
                                                <img src="{{ $item['image_url'] }}"
                                                    alt="{{ $item['name'] }}" class="img-fluid rounded"
                                                    style="width: 100px; height: 100px; object-fit: cover;">
                                            @else
                                                <img src="https://via.placeholder.com/100x100?text={{ urlencode($item['name']) }}"
                                                    alt="{{ $item['name'] }}" class="img-fluid rounded"
                                                    style="width: 100px; height: 100px; object-fit: cover;">
                                            @endif
                                        </div>
                                        <div class="flex-grow-1 ms-0 ms-md-4">
                                            <div class="d-flex flex-column flex-md-row justify-content-between align-items-start mb-2">
                                                <h5 class="fw-bold mb-1 mb-md-0">{{ $item['name'] }}</h5>
                                                <form action="{{ route('cart.remove', $id) }}" method="POST" class="d-inline cart-remove-form">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-link text-danger p-0 cart-remove-button">Remove</button>
                                                </form>
                                            </div>
                                            <p class="text-muted mb-2">
                                                {{ \App\Models\Vendor::find($item['vendor_id'])->shop_name ?? 'Unknown Vendor' }}
                                            </p>
                                            <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                                                <form action="{{ route('cart.update', $id) }}" method="POST" class="cart-update-form mb-2 mb-md-0" data-item-id="{{ $id }}">
                                                    @csrf
                                                    @method('PATCH')
                                                    <div class="input-group input-group-sm">
                                                        <button type="button" class="btn btn-outline-dark btn-sm quantity-btn" data-action="decrease" data-input-id="quantity-{{ $id }}">-</button>
                                                        <input type="number" name="quantity" id="quantity-{{ $id }}" value="{{ $item['quantity'] }}" min="0" class="form-control text-center cart-quantity" style="max-width: 60px;" data-price="{{ $item['price'] }}">
                                                        <button type="button" class="btn btn-outline-dark btn-sm quantity-btn" data-action="increase" data-input-id="quantity-{{ $id }}">+</button>
                                                    </div>
                                                    <button type="submit"
                                                        class="btn btn-sm btn-dark ms-3 update-btn d-none">Update</button>
                                                </form>
                                                <p class="fw-bold mb-0 item-subtotal" data-item-id="{{ $id }}">
                                                    ₦{{ number_format($item['price'] * $item['quantity'], 2) }}</p>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <div class="card-footer bg-white p-4 d-flex justify-content-between align-items-center">
                                <a href="{{ route('products.index') }}" class="text-decoration-none text-dark">
                                    <i class="fas fa-arrow-left me-2"></i> Continue Shopping
                                </a>
                                <form action="{{ route('cart.clear') }}" method="POST">
                                    @csrf
                                    <button type="submit" class="btn btn-outline-danger">
                                        <i class="fas fa-trash me-2"></i> Clear Cart
                                    </button>
                                </form>
                            </div>
                        @else
                            <div class="card-body p-5 text-center">
                                <div class="mb-4">
                                    <i class="fa-solid fa-shopping-cart fa-3x text-muted"></i>
                                </div>
                                <h3 class="fw-bold mb-3">Your cart is empty</h3>
                                <p class="text-muted mb-4">Looks like you haven't added any products to your cart yet.</p>
                                <a href="{{ route('products.index') }}" class="btn btn-dark px-4 py-2">
                                    Start Shopping
                                </a>
                            </div>
                        @endif
                    </div> {{-- End cart-items-container --}}

                    <div id="cart-empty-message" class="card-body p-5 text-center" {{ $cartItems->count() > 0 ? 'style="display: none;"' : '' }}>
                        <div class="mb-4">
                            <i class="fa-solid fa-shopping-cart fa-3x text-muted"></i>
                        </div>
                        <h3 class="fw-bold mb-3">Your cart is empty</h3>
                        <p class="text-muted mb-4">Looks like you haven't added any products to your cart yet.</p>
                        <a href="{{ route('products.index') }}" class="btn btn-dark px-4 py-2">
                            Start Shopping
                        </a>
                    </div>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="col-lg-4" id="order-summary-container" {{ $cartItems->count() === 0 ? 'style="display: none;"' : '' }}>
                <div class="card border-0 shadow sticky-top" style="top: 100px; z-index: 100;">
                    <div class="card-header bg-white py-3">
                        <h5 class="fw-bold mb-0">Order Summary</h5>
                    </div>
                    <div class="card-body">
                        @if ($cartItems->count() > 0)
                            <div class="d-flex justify-content-between mb-3">
                                <span>Subtotal</span>
                                <span class="fw-bold" id="cart-subtotal">₦{{ number_format($subtotal, 2) }}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span>Shipping</span>
                                <span class="fw-bold">₦0.00</span>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span>Tax (8%)</span>
                                <span class="fw-bold" id="cart-tax">₦{{ number_format($tax, 2) }}</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-4">
                                <span class="fw-bold">Total</span>
                                <span class="fw-bold fs-5" id="cart-total">₦{{ number_format($total, 2) }}</span>
                            </div>

                            <!-- Coupon Code -->
                            <div class="mb-4">
                                <label for="coupon" class="form-label fw-bold">Coupon Code</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="coupon"
                                        placeholder="Enter coupon code">
                                    <button class="btn btn-dark">Apply</button>
                                </div>
                            </div>

                            <!-- Checkout Button -->
                            <a href="{{ route('checkout.index') }}" class="btn btn-dark w-100 py-3 fw-bold">
                                Proceed to Checkout
                            </a>

                            <!-- Payment Methods -->
                            <div class="d-flex justify-content-center mt-4">
                                <img src="https://via.placeholder.com/40x25?text=Visa" alt="Visa" class="me-2">
                                <img src="https://via.placeholder.com/40x25?text=MC" alt="MasterCard" class="me-2">
                                <img src="https://via.placeholder.com/40x25?text=Amex" alt="American Express"
                                    class="me-2">
                                <img src="https://via.placeholder.com/40x25?text=PayPal" alt="PayPal">
                            </div>
                        @else
                            <div class="text-center py-4">
                                <p class="text-muted mb-0">No items in cart</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- You May Also Like -->
        <div class="row mt-5">
            <div class="col-12">
                <h3 class="fw-bold mb-4">You May Also Like</h3>
            </div>

            @if($recommendedProducts->count() > 0)
                @foreach($recommendedProducts as $product)
                    <div class="col-md-3 mb-4">
                        <div class="card h-100 border-2 hover-border-dark">
                            <div class="position-relative">
                                <a href="{{ route('products.show', $product->slug) }}">
                                    <img src="{{ $product->image_url ?? 'https://via.placeholder.com/300x300?text=Product' }}" 
                                         alt="{{ $product->name }}" class="card-img-top">
                                </a>
                                @if($product->is_featured)
                                    <div class="position-absolute top-0 start-0 bg-dark text-white px-2 py-1 m-2">
                                        <small>Featured</small>
                                    </div>
                                @endif
                                @if($product->discount_price && $product->discount_price < $product->price)
                                    <div class="position-absolute top-0 end-0 bg-danger text-white px-2 py-1 m-2">
                                        <small>Sale</small>
                                    </div>
                                @endif
                            </div>
                            <div class="card-body p-4">
                                <p class="text-muted small mb-1">{{ $product->category->name ?? 'Uncategorized' }}</p>
                                <h5 class="fw-semibold mb-1">{{ Str::limit($product->name, 40) }}</h5>
                                <div class="d-flex align-items-center mb-3">
                                    @if($product->discount_price && $product->discount_price < $product->price)
                                        <p class="fw-bold mb-0 me-2">₦{{ number_format($product->discount_price, 2) }}</p>
                                        <p class="text-muted mb-0"><s>₦{{ number_format($product->price, 2) }}</s></p>
                                    @else
                                        <p class="fw-bold mb-0">₦{{ number_format($product->price, 2) }}</p>
                                    @endif
                                </div>
                                <form action="{{ route('cart.add', $product) }}" method="POST" class="add-to-cart-form">
                                    @csrf
                                    <input type="hidden" name="quantity" value="1">
                                    <button type="submit" class="btn btn-dark w-100 d-flex align-items-center justify-content-center">
                                        <i class="fa-solid fa-cart-plus me-2"></i> Add to Cart
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                @endforeach
            @else
                <div class="col-12">
                    <div class="alert alert-light text-center">
                        <p class="mb-0">No product recommendations available.</p>
                    </div>
                </div>
            @endif

            <!-- Recommendations loaded dynamically via the controller -->
        </div>
    </div>
    @push('scripts')
        <script>
            console.log('Cart script initializing...');
            document.addEventListener('DOMContentLoaded', function() {
                console.log('Cart DOMContentLoaded.');
                const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                const cartItemsContainer = document.getElementById('cart-items-container');
                const cartEmptyMessage = document.getElementById('cart-empty-message');
                const orderSummaryContainer = document.getElementById('order-summary-container');

                if (!csrfToken) console.error('CSRF token not found!');
                if (!cartItemsContainer) console.error('cart-items-container not found!');
                if (!cartEmptyMessage) console.error('cart-empty-message not found!');
                if (!orderSummaryContainer) console.error('order-summary-container not found!');

                function updateCartTotals(subtotal, tax, total) {
                    console.log('Updating cart totals:', { subtotal, tax, total });
                    const subtotalEl = document.getElementById('cart-subtotal');
                    const taxEl = document.getElementById('cart-tax');
                    const totalEl = document.getElementById('cart-total');
                    if (subtotalEl) subtotalEl.textContent = `₦${parseFloat(subtotal).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
                    if (taxEl) taxEl.textContent = `₦${parseFloat(tax).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
                    if (totalEl) totalEl.textContent = `₦${parseFloat(total).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
                }

                function updateItemSubtotal(itemId, newSubtotal) {
                    console.log('Updating item subtotal for item ID:', itemId, 'New subtotal:', newSubtotal);
                    const itemSubtotalElement = document.querySelector(`.item-subtotal[data-item-id='${itemId}']`);
                    if (itemSubtotalElement) {
                        itemSubtotalElement.textContent = `₦${parseFloat(newSubtotal).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
                    } else {
                        console.warn('Item subtotal element not found for ID:', itemId);
                    }
                }

                function showAlert(message, type = 'success') {
                    console.log('Showing alert:', message, 'Type:', type);
                    const alertContainer = document.querySelector('.container.py-4 > .row:first-child');
                    if (!alertContainer) {
                        console.warn('Alert container not found for showing alert.');
                        return;
                    }
                    const existingAlert = alertContainer.querySelector('.alert-auto-dismiss');
                    if (existingAlert) existingAlert.remove();

                    const alertDiv = document.createElement('div');
                    alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                    alertDiv.setAttribute('role', 'alert');
                    alertDiv.innerHTML = `${message}<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>`;
                    alertContainer.insertBefore(alertDiv, alertContainer.firstChild);
                    setTimeout(() => {
                        const bsAlert = bootstrap.Alert.getOrCreateInstance(alertDiv);
                        if (bsAlert) bsAlert.close();
                    }, 5000);
                }

                function updateCartView(cartItemCount) {
                    console.log('Updating cart view. Item count:', cartItemCount);
                    if (cartItemCount === 0) {
                        if (cartItemsContainer) cartItemsContainer.style.display = 'none';
                        if (orderSummaryContainer) orderSummaryContainer.style.display = 'none';
                        if (cartEmptyMessage) cartEmptyMessage.style.display = 'block';
                    } else {
                        if (cartItemsContainer) cartItemsContainer.style.display = 'block';
                        if (orderSummaryContainer) orderSummaryContainer.style.display = 'block';
                        if (cartEmptyMessage) cartEmptyMessage.style.display = 'none';
                    }
                }

                function submitCartUpdate(formElement) {
                    console.log('submitCartUpdate called for form:', formElement);
                    const itemId = formElement.dataset.itemId;
                    const quantityInput = formElement.querySelector('input[name="quantity"]');
                    
                    if (!quantityInput) {
                        console.error('Quantity input not found in form:', formElement);
                        return;
                    }
                    let quantity = parseInt(quantityInput.value);
                    if (isNaN(quantity) || quantity < 0) {
                        console.warn('Invalid quantity detected, defaulting to 0:', quantityInput.value);
                        quantity = 0;
                        quantityInput.value = 0;
                    }

                    const url = formElement.action;
                    const updateBtn = formElement.querySelector('.update-btn');

                    console.log(`Preparing AJAX PATCH to ${url}`, { itemId, quantity });

                    if (updateBtn) {
                        updateBtn.innerHTML = '<span class="spinner-border spinner-border-sm"></span> Updating...';
                        updateBtn.disabled = true;
                        updateBtn.classList.remove('d-none');
                    }

                    fetch(url, {
                        method: 'PATCH',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrfToken,
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify({ quantity: quantity })
                    })
                    .then(response => {
                        console.log('Cart update response status:', response.status);
                        return response.json().then(data => ({ status: response.status, body: data }));
                    })
                    .then(({ status, body }) => {
                        console.log('Cart update response data:', body);
                        if (body.success) {
                            showAlert(body.message || 'Cart updated successfully!', 'success');
                            updateCartTotals(body.cart.subtotal, body.cart.tax, body.cart.total);
                            if (body.cart.item_subtotal !== undefined) {
                                updateItemSubtotal(itemId, body.cart.item_subtotal);
                            }
                            // If item quantity was set to 0, it's effectively removed.
                            if (quantity === 0) {
                                const itemRow = formElement.closest('.d-flex.p-4');
                                if (itemRow) {
                                    console.log('Removing item row due to quantity 0:', itemRow);
                                    itemRow.remove();
                                }
                            }
                            updateCartView(body.cart.count);
                        } else {
                            showAlert(body.message || `Failed to update cart (status: ${status}).`, 'danger');
                        }
                    })
                    .catch(error => {
                        console.error('Error updating cart:', error);
                        showAlert('An error occurred while updating the cart. Please try again.', 'danger');
                    })
                    .finally(() => {
                        if (updateBtn) {
                            updateBtn.innerHTML = 'Update';
                            updateBtn.disabled = false;
                            updateBtn.classList.add('d-none');
                        }
                    });
                }

                document.querySelectorAll('.quantity-btn').forEach(btn => {
                    console.log('Attaching listener to quantity button:', btn);
                    btn.addEventListener('click', function() {
                        console.log('Quantity button clicked:', this);
                        const action = this.dataset.action;
                        const inputId = this.dataset.inputId; // Corrected: inputId
                        
                        console.log(`Action: ${action}, Input ID: ${inputId}`);

                        if (!inputId) {
                            console.error('data-input-id attribute is missing on button:', this);
                            return;
                        }
                        const inputElement = document.getElementById(inputId);
                        const formElement = this.closest('form.cart-update-form');

                        if (!inputElement) {
                            console.error('Quantity input element not found for ID:', inputId, 'Button:', this);
                            return;
                        }
                        if (!formElement) {
                            console.error('Parent form not found for quantity button:', this);
                            return;
                        }

                        const maxQuantity = parseInt(inputElement.getAttribute('max')) || 999;
                        const minQuantity = parseInt(inputElement.getAttribute('min')) || 0;
                        let currentValue = parseInt(inputElement.value);

                        if (isNaN(currentValue)) currentValue = minQuantity;
                        console.log(`Current val: ${currentValue}, Min: ${minQuantity}, Max: ${maxQuantity}`);

                        if (action === 'increase') {
                            if (currentValue < maxQuantity) inputElement.value = currentValue + 1;
                        } else if (action === 'decrease') {
                            if (currentValue > minQuantity) inputElement.value = currentValue - 1;
                        }
                        console.log('New input value:', inputElement.value);
                        // Programmatically submit the form to trigger its 'submit' event listener
                        console.log('Requesting submit for form:', formElement);
                        if (formElement.requestSubmit) {
                            formElement.requestSubmit();
                        } else {
                            formElement.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
                        }
                    });
                });

                document.querySelectorAll('input.cart-quantity').forEach(input => {
                    console.log('Attaching listener to quantity input:', input);
                    input.addEventListener('change', function() {
                        console.log('Quantity input changed:', this);
                        const formElement = this.closest('form.cart-update-form');
                        if (!formElement) {
                            console.error('Parent form not found for quantity input:', this);
                            return;
                        }
                        let value = parseInt(this.value);
                        const minVal = parseInt(this.getAttribute('min')) || 0;
                        if (isNaN(value) || value < minVal) {
                            console.warn('Correcting input value from', value, 'to', minVal);
                            this.value = minVal;
                        }
                        console.log('Requesting submit for form due to input change:', formElement);
                        if (formElement.requestSubmit) {
                            formElement.requestSubmit();
                        } else {
                            formElement.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
                        }
                    });
                });

                document.querySelectorAll('form.cart-update-form').forEach(form => {
                    console.log('Attaching submit listener to cart update form:', form);
                    form.addEventListener('submit', function(e) {
                        console.log('Cart update form submitted. Preventing default for:', this, e);
                        e.preventDefault(); 
                        submitCartUpdate(this);
                    });
                });

                document.querySelectorAll('.cart-remove-form').forEach(form => {
                    console.log('Attaching submit listener to cart remove form:', form);
                    form.addEventListener('submit', function(e) {
                        console.log('Cart remove form submitted. Preventing default for:', this, e);
                        e.preventDefault();
                        const url = this.action;
                        const itemRow = this.closest('.d-flex.p-4');
                        if (!confirm('Are you sure you want to remove this item from your cart?')) return;

                        const removeButton = this.querySelector('.cart-remove-button');
                        if(removeButton) removeButton.innerHTML = '<span class="spinner-border spinner-border-sm"></span>';
                        console.log(`Preparing AJAX DELETE to ${url}`);

                        fetch(url, {
                            method: 'DELETE',
                            headers: { 'X-CSRF-TOKEN': csrfToken, 'Accept': 'application/json' }
                        })
                        .then(response => {
                            console.log('Cart remove response status:', response.status);
                            return response.json().then(data => ({ status: response.status, body: data }));
                        })
                        .then(({ status, body }) => {
                            console.log('Cart remove response data:', body);
                            if (body.success) {
                                showAlert(body.message || 'Item removed successfully!', 'success');
                                if (itemRow) {
                                    console.log('Removing item row:', itemRow);
                                    itemRow.remove();
                                }
                                updateCartTotals(body.cart.subtotal, body.cart.tax, body.cart.total);
                                updateCartView(body.cart.count);
                            } else {
                                showAlert(body.message || `Failed to remove item (status: ${status}).`, 'danger');
                            }
                        })
                        .catch(error => {
                            console.error('Error removing item:', error);
                            showAlert('An error occurred while removing the item.', 'danger');
                        })
                        .finally(() => {
                            if(removeButton) removeButton.textContent = 'Remove';
                        });
                    });
                });

                const initialCountStr = cartItemsContainer ? cartItemsContainer.dataset.initialCount : '0';
                const initialCartItemCount = parseInt(initialCountStr) || 0;
                console.log('Initial cart item count from data attribute:', initialCartItemCount);
                updateCartView(initialCartItemCount);
                console.log('Cart script initialized.');
            });
        </script>
    @endpush

@endsection
