/**
 * Vendor Dashboard JavaScript
 * 
 * This file handles all the interactive elements and data visualization
 * for the Brandify vendor dashboard including charts and map functionality.
 */

// Global chart instances
let revenueChart, categoryChart, customerChart;

// Main initialization function for all dashboard widgets
function initDashboardWidgets() {
    // Initialize charts
    initDashboardCharts();
    
    // Initialize Nigeria Map
    initNigeriaMapWidget();
    
    // Set up event listeners for timeframe switching
    setupChartTimeframeListeners();
    setupMapTimeframeListeners();
}

// Initialize all dashboard charts with properly formatted data
function initDashboardCharts() {
    // Initialize charts with empty data initially
    revenueChart = createRevenueChart('revenueChart');
    categoryChart = createCategoryChart('categoryChart');
    customerChart = createCustomerGrowthChart('customerChart');
    
    // Fetch initial data (default: last 6 months)
    fetchChartData('6months');
}

// Set up event listeners for chart timeframe switching
function setupChartTimeframeListeners() {
    const timeframeElements = document.querySelectorAll('.chart-timeframe');
    if (!timeframeElements.length) return;
    
    timeframeElements.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update active state in dropdown
            document.querySelectorAll('.chart-timeframe').forEach(el => el.classList.remove('active'));
            this.classList.add('active');
            
            // Update dropdown button text
            const period = this.getAttribute('data-period');
            let buttonText = 'Last 6 Months';
            if (period === 'week') buttonText = 'This Week';
            else if (period === 'month') buttonText = 'This Month';
            else if (period === 'year') buttonText = 'This Year';
            
            const dropdown = document.getElementById('chartTimeframeDropdown');
            if (dropdown) {
                dropdown.textContent = buttonText;
            }
            
            // Fetch data for the selected timeframe
            fetchChartData(period);
        });
    });
}

// Set up event listeners for map timeframe switching
function setupMapTimeframeListeners() {
    const timeframeElements = document.querySelectorAll('.map-timeframe');
    if (!timeframeElements.length) return;
    
    timeframeElements.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update active state in dropdown
            document.querySelectorAll('.map-timeframe').forEach(el => el.classList.remove('active'));
            this.classList.add('active');
            
            // Update dropdown button text
            const timeframe = this.getAttribute('data-timeframe');
            let buttonText = 'Last 30 Days';
            if (timeframe === '7') buttonText = 'Last 7 Days';
            else if (timeframe === '90') buttonText = 'Last 90 Days';
            else if (timeframe === 'all') buttonText = 'All Time';
            
            const dropdown = document.getElementById('mapTimeframeDropdown');
            if (dropdown) {
                dropdown.textContent = buttonText;
            }
            
            // Refresh map data
            refreshMapData(timeframe);
        });
    });
}

// Function to fetch chart data based on time period
function fetchChartData(period) {
    // Show loading state
    const chartError = document.getElementById('chart-error-message');
    const chartContainer = document.querySelector('.chart-container');
    
    if (chartError) chartError.style.display = 'none';
    if (chartContainer) chartContainer.classList.add('loading');
    
    // Add CSRF token to headers for Laravel
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    
    // Fetch data from the API endpoint
    fetch(`/api/vendor/analytics?period=${period}`, {
        headers: {
            'X-CSRF-TOKEN': csrfToken,
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        },
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // Update charts with the real data
        updateCharts(data);
        
        // Remove loading state
        if (chartContainer) chartContainer.classList.remove('loading');
    })
    .catch(error => {
        console.error('Error fetching chart data:', error);
        
        // Show error message
        if (chartError) {
            chartError.style.display = 'block';
        }
        
        // Use simulated data as fallback
        updateChartsWithSimulatedData(period);
        
        // Remove loading state
        if (chartContainer) chartContainer.classList.remove('loading');
    });
}

// Function to update all charts with real data
function updateCharts(data) {
    if (data.revenue_data && revenueChart) {
        updateRevenueChart(revenueChart, data.revenue_data);
    }
    
    if (data.category_data && categoryChart) {
        updateCategoryChart(categoryChart, data.category_data);
    }
    
    if (data.customer_data && customerChart) {
        updateCustomerChart(customerChart, data.customer_data);
    }
}

// Function to update charts with simulated data (fallback)
function updateChartsWithSimulatedData(period) {
    console.log('Using simulated data for period:', period);
    
    // Generate simulated data based on period
    const simulatedData = {
        revenue_data: generateSimulatedRevenueData(period),
        category_data: generateSimulatedCategoryData(),
        customer_data: generateSimulatedCustomerData(period)
    };
    
    // Update charts with simulated data
    updateCharts(simulatedData);
}

// Function to refresh map data
function refreshMapData(timeframe = '30') {
    const mapContainer = document.getElementById('nigeria-map-container');
    const loadingOverlay = document.getElementById('map-loading-overlay');
    const errorOverlay = document.getElementById('map-error-message');
    const stateOrdersTable = document.getElementById('state-orders-table');
    
    // Show loading state
    if (loadingOverlay) loadingOverlay.style.display = 'flex';
    if (errorOverlay) errorOverlay.style.display = 'none';
    if (stateOrdersTable) {
        stateOrdersTable.innerHTML = '<tr><td colspan="3" class="text-center py-3"><div class="spinner-border spinner-border-sm" role="status"></div> Loading...</td></tr>';
    }
    
    // Add CSRF token to headers for Laravel
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    
    // Fetch map data from the API endpoint
    fetch(`/api/vendor/orders/by-state?timeframe=${timeframe}`, {
        headers: {
            'X-CSRF-TOKEN': csrfToken,
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        },
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // Update map with the real data
        if (typeof updateNigeriaMap === 'function') {
            updateNigeriaMap(data);
        }
        
        // Update state orders table
        updateStateOrdersTable(data);
        
        // Hide loading state
        if (loadingOverlay) loadingOverlay.style.display = 'none';
    })
    .catch(error => {
        console.error('Error fetching map data:', error);
        
        // Show error message
        if (errorOverlay) errorOverlay.style.display = 'flex';
        
        // Hide loading state
        if (loadingOverlay) loadingOverlay.style.display = 'none';
        
        // Use simulated data as fallback
        if (typeof updateNigeriaMap === 'function') {
            updateNigeriaMap(generateSimulatedMapData());
        }
    });
}

// Function to update state orders table
function updateStateOrdersTable(data) {
    const stateOrdersTable = document.getElementById('state-orders-table');
    if (!stateOrdersTable) return;
    
    // Clear existing rows
    stateOrdersTable.innerHTML = '';
    
    // Check if we have data
    if (!data || Object.keys(data).length === 0) {
        stateOrdersTable.innerHTML = '<tr><td colspan="3" class="text-center py-3">No order data available</td></tr>';
        return;
    }
    
    // Sort states by order count (descending)
    const sortedStates = Object.entries(data)
        .sort((a, b) => b[1].order_count - a[1].order_count);
    
    // Add rows for each state
    sortedStates.forEach(([state, stateData]) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="ps-3">${state}</td>
            <td>${stateData.order_count || 0}</td>
            <td class="pe-3">₦${(stateData.order_value || 0).toLocaleString('en-NG', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
        `;
        stateOrdersTable.appendChild(row);
    });
}

// Initialize the dashboard when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on the vendor dashboard
    if (document.querySelector('.vendor-dashboard') || document.querySelector('.vendor-dashboard-container')) {
        initDashboardWidgets();
    }
});
