<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Services\DeliveryZoneService;

class VendorRegistrationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'contact_phone' => 'required|string|max:20',
            'business_name' => 'required|string|max:255|unique:vendors,business_name',
            'address_line1' => 'required|string|max:500',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255|in:' . implode(',', config('ecommerce.nigerian_market.states', [])),
            'contact_email_business' => 'nullable|email|max:255',
            'business_description' => 'required|string|max:1000',
            'logo' => 'nullable|image|mimes:jpg,jpeg,png,gif|max:2048',
            'id_document' => 'required|file|mimes:jpg,jpeg,png,pdf|max:5120',
            'business_document' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:5120',
            'terms' => 'required|accepted',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'business_name.unique' => 'This business name is already registered. Please choose a different name.',
            'state.in' => 'Please select a valid Nigerian state.',
            'contact_phone.required' => 'Please provide a valid Nigerian phone number.',
            'id_document.required' => 'Please upload a valid ID document (JPG, PNG, or PDF).',
            'terms.accepted' => 'You must accept the terms and conditions to register.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $deliveryZoneService = new DeliveryZoneService();
            
            // Validate Nigerian phone number format
            if ($this->contact_phone && !$deliveryZoneService->validateNigerianPhone($this->contact_phone)) {
                $validator->errors()->add('contact_phone', 'Please enter a valid Nigerian phone number (e.g., +234XXXXXXXXXX, 0XXXXXXXXXX).');
            }
        });
    }
}
