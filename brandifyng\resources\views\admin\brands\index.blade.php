@extends('layouts.admin')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Manage Brands</h1>
        <a href="{{ route('admin.brands.create') }}" class="btn btn-dark">
            <i class="fas fa-plus me-2"></i> Add New Brand
        </a>
    </div>

    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="card border-0 shadow-sm">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="bg-light">
                        <tr>
                            <th width="80">Logo</th>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Products</th>
                            <th>Status</th>
                            <th>Featured</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($brands as $brand)
                            <tr>
                                <td>
                                    @if ($brand->logo)
                                        <img src="{{ $brand->logo }}" alt="{{ $brand->name }}" class="img-fluid rounded" style="max-width: 50px; max-height: 50px;">
                                    @else
                                        <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                            <i class="fas fa-copyright text-muted"></i>
                                        </div>
                                    @endif
                                </td>
                                <td>{{ $brand->name }}</td>
                                <td>{{ Str::limit($brand->description, 50) }}</td>
                                <td>
                                    <span class="badge bg-info">{{ $brand->products->count() }}</span>
                                </td>
                                <td>
                                    @if ($brand->is_active)
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <span class="badge bg-secondary">Inactive</span>
                                    @endif
                                </td>
                                <td>
                                    <form action="{{ route('admin.brands.toggle-featured', $brand) }}" method="POST" class="d-inline">
                                        @csrf
                                        <button type="submit" class="btn btn-sm {{ $brand->is_featured ? 'btn-warning' : 'btn-outline-secondary' }}">
                                            @if ($brand->is_featured)
                                                <i class="fas fa-star"></i> Featured
                                            @else
                                                <i class="far fa-star"></i> Not Featured
                                            @endif
                                        </button>
                                    </form>
                                </td>
                                <td>
                                    <div class="d-flex">
                                        <a href="{{ route('admin.brands.edit', $brand) }}" class="btn btn-sm btn-outline-dark me-2">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('admin.brands.destroy', $brand) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this brand?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="fas fa-copyright fa-3x text-muted mb-3"></i>
                                        <h5 class="fw-bold mb-2">No Brands Found</h5>
                                        <p class="text-muted mb-3">You haven't added any brands yet.</p>
                                        <a href="{{ route('admin.brands.create') }}" class="btn btn-dark">
                                            <i class="fas fa-plus me-2"></i> Add Your First Brand
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="d-flex justify-content-center mt-4">
        {{ $brands->links() }}
    </div>
</div>
@endsection
