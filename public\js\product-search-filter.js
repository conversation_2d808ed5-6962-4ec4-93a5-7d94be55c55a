/**
 * Enhanced Product Search and Filtering
 * Provides AJAX-based search and filtering functionality
 */

class ProductSearchFilter {
    constructor() {
        this.searchInput = document.getElementById('search-input');
        this.searchBtn = document.getElementById('search-btn');
        this.categoryFilter = document.getElementById('category-filter');
        this.vendorFilter = document.getElementById('vendor-filter');
        this.minPriceInput = document.getElementById('min-price');
        this.maxPriceInput = document.getElementById('max-price');
        this.sortFilter = document.getElementById('sort-filter');
        this.productGridContainer = document.getElementById('product-grid-container');
        this.activeFiltersContainer = document.getElementById('active-filters');
        this.filterTagsContainer = document.getElementById('filter-tags');
        this.clearFiltersBtn = document.getElementById('clear-filters');
        
        this.debounceTimer = null;
        this.currentRequest = null;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.updateActiveFilters();
    }
    
    bindEvents() {
        // Search input with debounce
        if (this.searchInput) {
            this.searchInput.addEventListener('input', (e) => {
                clearTimeout(this.debounceTimer);
                this.debounceTimer = setTimeout(() => {
                    this.performSearch();
                }, 500);
            });
            
            this.searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    clearTimeout(this.debounceTimer);
                    this.performSearch();
                }
            });
        }
        
        // Search button
        if (this.searchBtn) {
            this.searchBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.performSearch();
            });
        }
        
        // Filter dropdowns
        [this.categoryFilter, this.vendorFilter, this.sortFilter].forEach(filter => {
            if (filter) {
                filter.addEventListener('change', () => {
                    this.performSearch();
                });
            }
        });
        
        // Price inputs with debounce
        [this.minPriceInput, this.maxPriceInput].forEach(input => {
            if (input) {
                input.addEventListener('input', () => {
                    clearTimeout(this.debounceTimer);
                    this.debounceTimer = setTimeout(() => {
                        this.performSearch();
                    }, 1000);
                });
            }
        });
        
        // Clear filters button
        if (this.clearFiltersBtn) {
            this.clearFiltersBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.clearAllFilters();
            });
        }
    }
    
    getFilterParams() {
        const params = new URLSearchParams();
        
        // Search query
        if (this.searchInput && this.searchInput.value.trim()) {
            params.set('query', this.searchInput.value.trim());
        }
        
        // Category filter
        if (this.categoryFilter && this.categoryFilter.value) {
            params.set('category', this.categoryFilter.value);
        }
        
        // Vendor filter
        if (this.vendorFilter && this.vendorFilter.value) {
            params.set('vendor', this.vendorFilter.value);
        }
        
        // Price range
        if (this.minPriceInput && this.minPriceInput.value) {
            params.set('min_price', this.minPriceInput.value);
        }
        if (this.maxPriceInput && this.maxPriceInput.value) {
            params.set('max_price', this.maxPriceInput.value);
        }
        
        // Sort
        if (this.sortFilter && this.sortFilter.value) {
            params.set('sort_by', this.sortFilter.value);
        }
        
        return params;
    }
    
    performSearch() {
        // Cancel previous request if still pending
        if (this.currentRequest) {
            this.currentRequest.abort();
        }
        
        const params = this.getFilterParams();
        const url = `/products?${params.toString()}`;
        
        this.showLoading();
        
        // Create AbortController for this request
        const controller = new AbortController();
        this.currentRequest = controller;
        
        fetch(url, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            signal: controller.signal
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            this.updateProductGrid(data.html_content);
            this.updateActiveFilters();
            this.updateURL(params);
            this.currentRequest = null;
        })
        .catch(error => {
            if (error.name !== 'AbortError') {
                console.error('Search error:', error);
                this.showError('Failed to load products. Please try again.');
            }
            this.currentRequest = null;
        });
    }
    
    showLoading() {
        if (this.productGridContainer) {
            this.productGridContainer.innerHTML = `
                <div class="d-flex justify-content-center align-items-center" style="min-height: 300px;">
                    <div class="text-center">
                        <div class="spinner-border text-dark mb-3" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="text-muted">Searching products...</p>
                    </div>
                </div>
            `;
        }
    }
    
    showError(message) {
        if (this.productGridContainer) {
            this.productGridContainer.innerHTML = `
                <div class="col-12">
                    <div class="alert alert-danger text-center">
                        <i class="fas fa-exclamation-triangle mb-2"></i>
                        <p class="mb-0">${message}</p>
                    </div>
                </div>
            `;
        }
    }
    
    updateProductGrid(htmlContent) {
        if (this.productGridContainer && htmlContent) {
            this.productGridContainer.innerHTML = htmlContent;
            
            // Re-initialize any JavaScript functionality for new content
            if (window.initializeAjaxAddToCart) {
                window.initializeAjaxAddToCart();
            }
        }
    }
    
    updateActiveFilters() {
        if (!this.activeFiltersContainer || !this.filterTagsContainer) return;
        
        const params = this.getFilterParams();
        const filterTags = [];
        
        // Create filter tags
        for (const [key, value] of params.entries()) {
            if (value) {
                let label = '';
                let displayValue = value;
                
                switch (key) {
                    case 'query':
                        label = 'Search';
                        displayValue = `"${value}"`;
                        break;
                    case 'category':
                        label = 'Category';
                        displayValue = this.categoryFilter?.selectedOptions[0]?.text || value;
                        break;
                    case 'vendor':
                        label = 'Vendor';
                        displayValue = this.vendorFilter?.selectedOptions[0]?.text || value;
                        break;
                    case 'min_price':
                        label = 'Min Price';
                        displayValue = `₦${value}`;
                        break;
                    case 'max_price':
                        label = 'Max Price';
                        displayValue = `₦${value}`;
                        break;
                    case 'sort_by':
                        label = 'Sort';
                        displayValue = this.sortFilter?.selectedOptions[0]?.text || value;
                        break;
                }
                
                filterTags.push(`
                    <span class="badge bg-secondary me-1 mb-1">
                        ${label}: ${displayValue}
                        <button type="button" class="btn-close btn-close-white ms-1" 
                                data-filter-key="${key}" style="font-size: 0.7em;"></button>
                    </span>
                `);
            }
        }
        
        if (filterTags.length > 0) {
            this.filterTagsContainer.innerHTML = filterTags.join('');
            this.activeFiltersContainer.style.display = 'block';
            
            // Bind remove filter events
            this.filterTagsContainer.querySelectorAll('.btn-close').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const filterKey = e.target.getAttribute('data-filter-key');
                    this.removeFilter(filterKey);
                });
            });
        } else {
            this.activeFiltersContainer.style.display = 'none';
        }
    }
    
    removeFilter(filterKey) {
        switch (filterKey) {
            case 'query':
                if (this.searchInput) this.searchInput.value = '';
                break;
            case 'category':
                if (this.categoryFilter) this.categoryFilter.value = '';
                break;
            case 'vendor':
                if (this.vendorFilter) this.vendorFilter.value = '';
                break;
            case 'min_price':
                if (this.minPriceInput) this.minPriceInput.value = '';
                break;
            case 'max_price':
                if (this.maxPriceInput) this.maxPriceInput.value = '';
                break;
            case 'sort_by':
                if (this.sortFilter) this.sortFilter.value = 'newest';
                break;
        }
        
        this.performSearch();
    }
    
    clearAllFilters() {
        if (this.searchInput) this.searchInput.value = '';
        if (this.categoryFilter) this.categoryFilter.value = '';
        if (this.vendorFilter) this.vendorFilter.value = '';
        if (this.minPriceInput) this.minPriceInput.value = '';
        if (this.maxPriceInput) this.maxPriceInput.value = '';
        if (this.sortFilter) this.sortFilter.value = 'newest';
        
        this.performSearch();
    }
    
    updateURL(params) {
        const newUrl = new URL(window.location.origin + window.location.pathname);
        for (const [key, value] of params.entries()) {
            if (value) {
                newUrl.searchParams.set(key, value);
            }
        }
        
        if (newUrl.href !== window.location.href) {
            history.pushState({ path: newUrl.href }, '', newUrl.href);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if we're on a page with the search form
    if (document.getElementById('product-filter-form')) {
        window.productSearchFilter = new ProductSearchFilter();
    }
});
