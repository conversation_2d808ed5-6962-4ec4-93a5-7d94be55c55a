@extends('layouts.app')

@section('content')
<div class="container py-5">
    <div class="row">
        <div class="col-lg-3 mb-4">
            <!-- Dashboard Sidebar -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-4">
                        <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3" style="width: 64px; height: 64px;">
                            <span class="fs-4 fw-bold text-dark">{{ substr(auth()->user()->name, 0, 1) }}</span>
                        </div>
                        <div>
                            <h5 class="fw-bold mb-1">{{ auth()->user()->name }}</h5>
                            <p class="text-muted mb-0 small">{{ auth()->user()->email }}</p>
                        </div>
                    </div>
                    
                    <div class="list-group list-group-flush">
                        <a href="{{ route('dashboard') }}" class="list-group-item list-group-item-action {{ request()->routeIs('dashboard') ? 'active fw-bold' : '' }}">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                        <a href="{{ route('orders.index') }}" class="list-group-item list-group-item-action {{ request()->routeIs('orders.*') ? 'active fw-bold' : '' }}">
                            <i class="fas fa-shopping-bag me-2"></i> My Orders
                        </a>
                        <a href="{{ route('wishlist.index') }}" class="list-group-item list-group-item-action {{ request()->routeIs('wishlist.*') ? 'active fw-bold' : '' }}">
                            <i class="fas fa-heart me-2"></i> Wishlist
                        </a>
                        <a href="{{ route('settings.profile') }}" class="list-group-item list-group-item-action {{ request()->routeIs('settings.*') ? 'active fw-bold' : '' }}">
                            <i class="fas fa-user-cog me-2"></i> Account Settings
                        </a>
                    </div>
                </div>
            </div>

            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <h5 class="fw-bold mb-3">Need Help?</h5>
                    <p class="text-muted mb-3 small">Have questions or concerns about your account or orders?</p>
                    <a href="{{ route('contact') }}" class="btn btn-dark w-100">
                        <i class="fas fa-headset me-2"></i> Contact Support
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-lg-9">
            <!-- Welcome Card -->
            <div class="card border-0 shadow-sm mb-4 bg-dark text-white">
                <div class="card-body p-4">
                    <h4 class="fw-bold mb-3">Welcome back, {{ auth()->user()->name }}!</h4>
                    <p class="mb-0">Manage your orders, wishlists, and account information from your personal dashboard.</p>
                </div>
            </div>
            
            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-4 mb-3 mb-md-0">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between mb-3">
                                <h5 class="fw-bold mb-0">Orders</h5>
                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                    <i class="fas fa-shopping-bag text-dark"></i>
                                </div>
                            </div>
                            <h2 class="fw-bold mb-0">{{ auth()->user()->orders()->count() }}</h2>
                            <p class="text-muted mb-0">Total orders placed</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-3 mb-md-0">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between mb-3">
                                <h5 class="fw-bold mb-0">Wishlist</h5>
                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                    <i class="fas fa-heart text-dark"></i>
                                </div>
                            </div>
                            <h2 class="fw-bold mb-0">{{ auth()->user()->wishlistItems()->count() ?? 0 }}</h2>
                            <p class="text-muted mb-0">Saved products</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between mb-3">
                                <h5 class="fw-bold mb-0">Reviews</h5>
                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                    <i class="fas fa-star text-dark"></i>
                                </div>
                            </div>
                            <h2 class="fw-bold mb-0">{{ auth()->user()->reviews()->count() ?? 0 }}</h2>
                            <p class="text-muted mb-0">Product reviews</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Orders -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="fw-bold mb-0">Recent Orders</h5>
                    <a href="{{ route('orders.index') }}" class="btn btn-sm btn-dark">View All</a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th>Order #</th>
                                    <th>Date</th>
                                    <th>Items</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse(auth()->user()->orders()->latest()->take(5)->get() as $order)
                                    <tr>
                                        <td>{{ $order->order_number }}</td>
                                        <td>{{ $order->created_at->format('M d, Y') }}</td>
                                        <td>{{ $order->items()->count() }}</td>
                                        <td>${{ number_format($order->total, 2) }}</td>
                                        <td>
                                            @if($order->status == 'pending')
                                                <span class="badge bg-warning text-dark">Pending</span>
                                            @elseif($order->status == 'processing')
                                                <span class="badge bg-info">Processing</span>
                                            @elseif($order->status == 'completed')
                                                <span class="badge bg-success">Completed</span>
                                            @elseif($order->status == 'cancelled')
                                                <span class="badge bg-danger">Cancelled</span>
                                            @else
                                                <span class="badge bg-secondary">{{ ucfirst($order->status) }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <a href="{{ route('orders.show', $order->id) }}" class="btn btn-sm btn-outline-dark">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                                                <h5 class="fw-bold mb-2">No Orders Yet</h5>
                                                <p class="text-muted mb-3">You haven't placed any orders yet.</p>
                                                <a href="{{ route('products.index') }}" class="btn btn-dark">
                                                    <i class="fas fa-shopping-cart me-2"></i> Start Shopping
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Recommended Products -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white py-3">
                    <h5 class="fw-bold mb-0">Recommended For You</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach(App\Models\Product::inRandomOrder()->where('is_active', true)->limit(4)->get() as $product)
                            <div class="col-md-3 mb-3 mb-md-0">
                                <div class="card h-100 border-0 shadow-sm hover-shadow">
                                    <img src="{{ $product->image_url ?? 'https://via.placeholder.com/300x300?text=Product' }}" class="card-img-top" alt="{{ $product->name }}">
                                    <div class="card-body p-3">
                                        <p class="text-muted small mb-1">{{ $product->category->name ?? 'Uncategorized' }}</p>
                                        <h6 class="card-title fw-bold mb-1">{{ Str::limit($product->name, 40) }}</h6>
                                        <p class="fw-bold mb-2">${{ number_format($product->price, 2) }}</p>
                                        <a href="{{ route('products.show', $product->slug) }}" class="btn btn-sm btn-dark w-100">
                                            <i class="fas fa-eye me-1"></i> View
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
