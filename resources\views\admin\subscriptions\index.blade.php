@extends('layouts.admin')

@section('title', 'Subscription Management')

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Subscription Management</h1>
                </div>

                <!-- Subscription Statistics -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary">{{ $stats['total'] }}</h5>
                                <p class="card-text small text-muted">Total Vendors</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success">{{ $stats['active'] }}</h5>
                                <p class="card-text small text-muted">Active</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-warning">{{ $stats['pending'] }}</h5>
                                <p class="card-text small text-muted">Pending</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-secondary">{{ $stats['inactive'] }}</h5>
                                <p class="card-text small text-muted">Inactive</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-danger">{{ $stats['cancelled'] }}</h5>
                                <p class="card-text small text-muted">Cancelled</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" action="{{ route('admin.subscriptions.index') }}" class="row g-3">
                            <div class="col-md-4">
                                <input type="text" name="search" class="form-control" placeholder="Search vendors..."
                                    value="{{ request('search') }}">
                            </div>
                            <div class="col-md-3">
                                <select name="status" class="form-select">
                                    <option value="">All Statuses</option>
                                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active
                                    </option>
                                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending
                                    </option>
                                    <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>
                                        Inactive</option>
                                    <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>
                                        Cancelled</option>
                                    <option value="suspended" {{ request('status') == 'suspended' ? 'selected' : '' }}>
                                        Suspended</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary">Filter</button>
                            </div>
                            <div class="col-md-3 text-end">
                                <a href="{{ route('admin.subscriptions.index') }}" class="btn btn-outline-secondary">Clear
                                    Filters</a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Subscriptions Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Vendor Subscriptions</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="bg-light">
                                    <tr>
                                        <th>Vendor</th>
                                        <th>Email</th>
                                        <th>Shop Name</th>
                                        <th>Status</th>
                                        <th>Approved</th>
                                        <th>Joined</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($vendors as $vendor)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="{{ $vendor->logo ?? 'https://via.placeholder.com/40x40?text=V' }}"
                                                        alt="{{ $vendor->shop_name }}" width="40" height="40"
                                                        class="rounded me-2">
                                                    <div>
                                                        <div class="fw-medium">{{ $vendor->user->name }}</div>
                                                        <small class="text-muted">ID: {{ $vendor->id }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ $vendor->user->email }}</td>
                                            <td>{{ $vendor->shop_name }}</td>
                                            <td>
                                                @php
                                                    $statusColors = [
                                                        'active' => 'success',
                                                        'pending' => 'warning',
                                                        'inactive' => 'secondary',
                                                        'cancelled' => 'danger',
                                                        'suspended' => 'dark',
                                                    ];
                                                    $color = $statusColors[$vendor->subscription_status] ?? 'secondary';
                                                @endphp
                                                <span
                                                    class="badge bg-{{ $color }}">{{ ucfirst($vendor->subscription_status) }}</span>
                                            </td>
                                            <td>
                                                @if ($vendor->approved)
                                                    <span class="badge bg-success">Yes</span>
                                                @else
                                                    <span class="badge bg-danger">No</span>
                                                @endif
                                            </td>
                                            <td>{{ $vendor->created_at->format('M d, Y') }}</td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="{{ route('admin.subscriptions.edit', $vendor) }}"
                                                        class="btn btn-outline-primary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    @if ($vendor->subscription_status !== 'active')
                                                        <form action="{{ route('admin.subscriptions.activate', $vendor) }}"
                                                            method="POST" class="d-inline">
                                                            @csrf
                                                            @method('PATCH')
                                                            <button type="submit" class="btn btn-outline-success"
                                                                title="Activate">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                        </form>
                                                    @endif
                                                    @if ($vendor->subscription_status === 'active')
                                                        <form action="{{ route('admin.subscriptions.suspend', $vendor) }}"
                                                            method="POST" class="d-inline">
                                                            @csrf
                                                            @method('PATCH')
                                                            <button type="submit" class="btn btn-outline-warning"
                                                                title="Suspend">
                                                                <i class="fas fa-pause"></i>
                                                            </button>
                                                        </form>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="7" class="text-center py-4">
                                                <div class="text-muted">
                                                    <i class="fas fa-inbox fa-2x mb-2"></i>
                                                    <p>No vendors found.</p>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                    @if ($vendors->hasPages())
                        <div class="card-footer">
                            {{ $vendors->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection
