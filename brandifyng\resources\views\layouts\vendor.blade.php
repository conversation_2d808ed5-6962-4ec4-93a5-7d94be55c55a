<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>{{ $title ?? config('app.name', 'Brandify') }} - Vendor</title>
    
    <!-- Bootstrap 5 CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
    <link href="https://fonts.bunny.net/css?family=figtree:400,600&display=swap" rel="stylesheet" />
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    
    <!-- Alpine.js CDN -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js"></script>
    
    <!-- Responsive fixes CSS -->
    <link rel="stylesheet" href="{{ asset('css/responsive-fixes.css') }}">
    
    <!-- Custom Styles -->
    <style>
        :root {
            --primary-color: #000;
            --secondary-color: #fff;
            --text-color: #000;
            --bg-color: #fff;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-400: #ced4da;
            --gray-500: #adb5bd;
            --gray-600: #6c757d;
            --gray-700: #495057;
            --gray-800: #343a40;
            --gray-900: #212529;
        }
        
        body {
            font-family: 'Figtree', 'Instrument Sans', sans-serif;
            color: var(--text-color);
            background-color: var(--gray-100);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            min-height: 100vh;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--secondary-color);
        }
        
        .btn-primary:hover {
            background-color: var(--gray-800);
            border-color: var(--gray-800);
        }
        
        .sidebar {
            width: 280px;
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 1000;
            background-color: var(--primary-color);
            color: var(--secondary-color);
            transition: all 0.3s;
            overflow-y: auto;
        }
        
        .sidebar .nav-link {
            color: var(--gray-400);
            padding: 0.75rem 1.25rem;
            border-radius: 0.25rem;
            margin-bottom: 0.25rem;
            transition: all 0.2s;
        }
        
        .sidebar .nav-link:hover {
            color: var(--secondary-color);
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .nav-link.active {
            color: var(--secondary-color);
            background-color: rgba(255, 255, 255, 0.2);
        }
        
        .sidebar .nav-link i {
            width: 24px;
            text-align: center;
            margin-right: 0.5rem;
        }
        
        .content {
            margin-left: 280px;
            padding: 2rem;
            transition: all 0.3s;
        }
        
        /* Mobile sidebar toggle */
        .sidebar-toggle {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 1050;
            padding: 8px 12px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            display: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        .sidebar-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0,0,0,0.5);
            z-index: 999;
        }
        
        @media (max-width: 991px) {
            .sidebar {
                margin-left: -280px;
                box-shadow: 5px 0 10px rgba(0,0,0,0.1);
            }
            
            .sidebar.show {
                margin-left: 0;
            }
            
            .content {
                margin-left: 0;
                padding: 1.5rem 1rem;
            }
            
            .sidebar-toggle {
                display: block;
            }
            
            .sidebar-overlay.show {
                display: block;
            }
            
            /* Ensure the toggle button is visible when sidebar is shown */
            .sidebar.show + .sidebar-toggle {
                left: 290px;
            }
        }
        
        /* Improve responsiveness for all card content */
        @media (max-width: 767px) {
            .card {
                margin-bottom: 1rem;
            }
            
            .card-body {
                padding: 1rem;
            }
            
            .container-fluid {
                padding: 1rem 0.5rem;
            }
            
            /* Adjust row gutters */
            .row > [class*="col-"] {
                padding-right: 0.5rem;
                padding-left: 0.5rem;
            }
        }
        
        .card {
            border-radius: 0.5rem;
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .card-header {
            background-color: var(--secondary-color);
            border-bottom: 1px solid var(--gray-200);
            padding: 1.25rem 1.5rem;
        }
        
        .table th {
            font-weight: 600;
            color: var(--gray-700);
        }
    </style>
    
    @livewireStyles
</head>
<body>
    <div class="d-flex position-relative">
    <!-- Sidebar Toggle Button for Mobile -->
    <button id="sidebar-toggle" class="sidebar-toggle">
        <i class="fas fa-bars"></i>
    </button>
    
    <!-- Overlay for mobile when sidebar is open -->
    <div id="sidebar-overlay" class="sidebar-overlay"></div>
        <!-- Sidebar -->
        <nav id="sidebar" class="sidebar">
            <div class="p-4">
                <h3 class="fw-bold mb-4">{{ config('app.name', 'Brandify') }}</h3>
                
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a href="{{ route('vendor.dashboard') }}" class="nav-link {{ request()->routeIs('vendor.dashboard') ? 'active' : '' }}">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="{{ route('vendor.products.index') }}" class="nav-link {{ request()->routeIs('vendor.products.*') ? 'active' : '' }}">
                            <i class="fas fa-box"></i> Products
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="{{ route('vendor.orders.index') }}" class="nav-link {{ request()->routeIs('vendor.orders.*') ? 'active' : '' }}">
                            <i class="fas fa-shopping-cart"></i> Orders
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="{{ route('vendor.earnings.index') }}" class="nav-link {{ request()->routeIs('vendor.earnings.*') ? 'active' : '' }}">
                            <i class="fas fa-money-bill-wave"></i> Earnings
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="{{ route('vendor.subscription.index') }}" class="nav-link {{ request()->routeIs('vendor.subscription.*') ? 'active' : '' }}">
                            <i class="fas fa-credit-card"></i> Subscription
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="{{ route('vendor.settings.index') }}" class="nav-link {{ request()->routeIs('vendor.settings.*') ? 'active' : '' }}">
                            <i class="fas fa-store-alt"></i> Shop Settings
                        </a>
                    </li>
                    
                    <li class="nav-item mt-4">
                        <a href="{{ route('home') }}" class="nav-link">
                            <i class="fas fa-home"></i> Back to Site
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="nav-link border-0 bg-transparent">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Content -->
        <div class="content flex-grow-1">
            <!-- Top Navbar -->
            <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm mb-4 rounded">
                <div class="container-fluid">
                    <button id="sidebarToggle" class="btn d-md-none">
                        <i class="fas fa-bars"></i>
                    </button>
                    
                    <div class="d-flex align-items-center">
                        <h5 class="mb-0 ms-2">{{ $title ?? 'Vendor Dashboard' }}</h5>
                    </div>
                    
                    <div class="d-flex align-items-center">
                        <div class="dropdown">
                            <a href="#" class="d-flex align-items-center text-dark text-decoration-none dropdown-toggle" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <span class="me-2">{{ auth()->user()->name }}</span>
                                <div class="rounded-circle bg-dark text-white d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                    {{ auth()->user()->initials() }}
                                </div>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="{{ route('vendor.profile') }}">Profile</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit" class="dropdown-item">Logout</button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>
            
            <!-- Main Content -->
            <div class="container-fluid">
                @yield('content')
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
    
    <!-- Sidebar Toggle Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebarOverlay = document.getElementById('sidebar-overlay');
            
            function toggleSidebar() {
                sidebar.classList.toggle('show');
                sidebarOverlay.classList.toggle('show');
            }
            
            sidebarToggle.addEventListener('click', toggleSidebar);
            sidebarOverlay.addEventListener('click', toggleSidebar);
            
            // Close sidebar when window is resized to desktop size
            window.addEventListener('resize', function() {
                if (window.innerWidth > 991 && sidebar.classList.contains('show')) {
                    sidebar.classList.remove('show');
                    sidebarOverlay.classList.remove('show');
                }
            });
        });
    </script>
    
                    sidebar.classList.remove('show');
                }
            });
        });
    </script>
    
    <!-- Stacked Scripts -->
    @stack('scripts')
    
    <!-- Page Specific Scripts -->
    @yield('page-scripts')
    
    @livewireScripts
</body>
</html>
