<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Product;
use App\Services\ViewedProductsService;
use Illuminate\Http\Request;

class OrderController extends Controller
{
    /**
     * The viewed products service instance.
     *
     * @var \App\Services\ViewedProductsService
     */
    protected $viewedProductsService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\ViewedProductsService  $viewedProductsService
     * @return void
     */
    public function __construct(ViewedProductsService $viewedProductsService)
    {
        $this->viewedProductsService = $viewedProductsService;
    }
    
    /**
     * Display a listing of the user's orders.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $orders = auth()->user()->orders()->latest()->paginate(10);
        $recentlyViewedProducts = $this->viewedProductsService->getRecentlyViewedProducts(3);
        
        return view('customer.orders.index', compact('orders', 'recentlyViewedProducts'));
    }

    /**
     * Display the specified order.
     *
     * @param  \App\Models\Order  $order
     * @return \Illuminate\View\View
     */
    public function show(Order $order)
    {
        // Make sure the order belongs to the authenticated user
        if ($order->user_id !== auth()->id()) {
            return redirect()->route('orders.index')->with('error', 'You do not have access to this order.');
        }

        return view('customer.orders.show', compact('order'));
    }

    /**
     * Cancel the specified order.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Order  $order
     * @return \Illuminate\Http\RedirectResponse
     */
    public function cancel(Request $request, Order $order)
    {
        // Make sure the order belongs to the authenticated user
        if ($order->user_id !== auth()->id()) {
            return redirect()->route('orders.index')->with('error', 'You do not have access to this order.');
        }

        // Check if the order can be cancelled
        if (!$order->isPending() && !$order->isProcessing()) {
            return redirect()->route('orders.show', $order)->with('error', 'This order cannot be cancelled.');
        }

        // Update order status
        $order->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
        ]);

        return redirect()->route('orders.show', $order)->with('success', 'Order has been cancelled successfully.');
    }

    /**
     * Request return for the specified order.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Order  $order
     * @return \Illuminate\Http\RedirectResponse
     */
    public function requestReturn(Request $request, Order $order)
    {
        // Make sure the order belongs to the authenticated user
        if ($order->user_id !== auth()->id()) {
            return redirect()->route('orders.index')->with('error', 'You do not have access to this order.');
        }

        // Check if the order is completed and can be returned
        if (!$order->isCompleted()) {
            return redirect()->route('orders.show', $order)->with('error', 'Only completed orders can be returned.');
        }

        // Validate the return reason
        $request->validate([
            'return_reason' => 'required|string|max:500',
        ]);

        // Update order status
        $order->update([
            'status' => 'return_requested',
            'return_reason' => $request->return_reason,
            'return_requested_at' => now(),
        ]);

        return redirect()->route('orders.show', $order)->with('success', 'Return request submitted successfully.');
    }
}
