@extends('layouts.admin')
@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="fw-bold">Vendor Details</h2>
        <a href="{{ route('admin.vendors.index') }}" class="btn btn-outline-dark">Back</a>
    </div>
    <div class="card">
        <div class="card-body">
            <h4 class="mb-3 fw-bold">{{ $vendor->shop_name }}</h4>
            <div class="mb-2"><strong>User:</strong> {{ $vendor->user->name ?? '' }} ({{ $vendor->user->email ?? '' }})</div>
            <div class="mb-2"><strong>Status:</strong> @if($vendor->is_approved) <span class="badge bg-success">Approved</span> @else <span class="badge bg-warning text-dark">Pending</span> @endif</div>
            <div class="mb-2"><strong>Slug:</strong> {{ $vendor->slug }}</div>
            <div class="mb-2"><strong>Description:</strong> {{ $vendor->description }}</div>
            <div class="mb-2"><strong>Address:</strong> {{ $vendor->address }}, {{ $vendor->city }}, {{ $vendor->state }}, {{ $vendor->country }}</div>
            <div class="mb-2"><strong>Created:</strong> {{ $vendor->created_at->format('M d, Y') }}</div>
        </div>
    </div>
</div>
@endsection
