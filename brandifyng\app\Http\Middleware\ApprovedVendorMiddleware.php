<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ApprovedVendorMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check() || !auth()->user()->isApprovedVendor()) {
            if (auth()->check() && auth()->user()->isVendor()) {
                return redirect()->route('vendor.pending');
            }
            
            abort(403, 'Unauthorized action.');
        }

        return $next($request);
    }
}
