<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class VendorMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        if (!auth()->user()->isVendor()) {
            abort(403, 'Access denied. Vendor access required.');
        }

        $vendor = auth()->user()->vendor;

        if (!$vendor) {
            return redirect()->route('vendor.register')
                ->with('error', 'Please complete your vendor registration.');
        }

        // Check if vendor is approved
        if (!$vendor->approved && !$vendor->is_approved) {
            return redirect()->route('vendor.pending')
                ->with('info', 'Your vendor account is pending approval.');
        }

        // Check subscription status for certain routes
        $restrictedRoutes = [
            'vendor.products.*',
            'vendor.orders.*',
            'vendor.earnings.*'
        ];

        $currentRoute = $request->route()->getName();
        $isRestrictedRoute = false;

        foreach ($restrictedRoutes as $pattern) {
            if (fnmatch($pattern, $currentRoute)) {
                $isRestrictedRoute = true;
                break;
            }
        }

        if ($isRestrictedRoute && $vendor->subscription_status !== 'active') {
            return redirect()->route('vendor.subscription.index')
                ->with('warning', 'Please activate your subscription to access this feature.');
        }

        return $next($request);
    }
}
