@extends('layouts.vendor')

@section('title', 'Shop Settings')

@section('content')
<div class="container-fluid px-0 px-md-3">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Shop Settings</h1>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-store me-2"></i>Shop Information
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('vendor.settings.update') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="shop_name" class="form-label fw-bold">Shop Name</label>
                                    <input type="text" class="form-control @error('shop_name') is-invalid @enderror" 
                                           id="shop_name" name="shop_name" 
                                           value="{{ old('shop_name', auth()->user()->vendor->shop_name) }}" required>
                                    @error('shop_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="slug" class="form-label fw-bold">Shop URL</label>
                                    <div class="input-group">
                                        <span class="input-group-text">{{ url('/store') }}/</span>
                                        <input type="text" class="form-control @error('slug') is-invalid @enderror" 
                                               id="slug" name="slug" 
                                               value="{{ old('slug', auth()->user()->vendor->slug) }}" required>
                                    </div>
                                    @error('slug')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label fw-bold">Shop Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="4" required>{{ old('description', auth()->user()->vendor->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="brand_description" class="form-label fw-bold">Brand Description</label>
                            <textarea class="form-control @error('brand_description') is-invalid @enderror" 
                                      id="brand_description" name="brand_description" rows="3" 
                                      placeholder="Describe your brand identity and values">{{ old('brand_description', auth()->user()->vendor->brand_description) }}</textarea>
                            @error('brand_description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">This will be displayed as your brand information since vendors represent their own brands.</small>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="logo" class="form-label fw-bold">Shop Logo</label>
                                    <input type="file" class="form-control @error('logo') is-invalid @enderror" 
                                           id="logo" name="logo" accept="image/*">
                                    @error('logo')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    @if(auth()->user()->vendor->logo)
                                        <div class="mt-2">
                                            <img src="{{ Storage::url(auth()->user()->vendor->logo) }}" 
                                                 alt="Current Logo" class="img-thumbnail" style="max-width: 100px;">
                                        </div>
                                    @endif
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="brand_logo" class="form-label fw-bold">Brand Logo</label>
                                    <input type="file" class="form-control @error('brand_logo') is-invalid @enderror" 
                                           id="brand_logo" name="brand_logo" accept="image/*">
                                    @error('brand_logo')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    @if(auth()->user()->vendor->brand_logo)
                                        <div class="mt-2">
                                            <img src="{{ Storage::url(auth()->user()->vendor->brand_logo) }}" 
                                                 alt="Current Brand Logo" class="img-thumbnail" style="max-width: 100px;">
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">
                        <h6 class="text-uppercase text-muted mb-3">Contact Information</h6>

                        <div class="mb-3">
                            <label for="address" class="form-label fw-bold">Address</label>
                            <input type="text" class="form-control @error('address') is-invalid @enderror" 
                                   id="address" name="address" 
                                   value="{{ old('address', auth()->user()->vendor->address) }}" required>
                            @error('address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="city" class="form-label fw-bold">City</label>
                                    <input type="text" class="form-control @error('city') is-invalid @enderror" 
                                           id="city" name="city" 
                                           value="{{ old('city', auth()->user()->vendor->city) }}" required>
                                    @error('city')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="state" class="form-label fw-bold">State</label>
                                    <select class="form-select @error('state') is-invalid @enderror" id="state" name="state" required>
                                        <option value="">Select State</option>
                                        @foreach(['Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa', 'Benue', 'Borno', 'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu', 'FCT', 'Gombe', 'Imo', 'Jigawa', 'Kaduna', 'Kano', 'Katsina', 'Kebbi', 'Kogi', 'Kwara', 'Lagos', 'Nasarawa', 'Niger', 'Ogun', 'Ondo', 'Osun', 'Oyo', 'Plateau', 'Rivers', 'Sokoto', 'Taraba', 'Yobe', 'Zamfara'] as $state)
                                            <option value="{{ $state }}" {{ old('state', auth()->user()->vendor->state) == $state ? 'selected' : '' }}>
                                                {{ $state }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('state')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="country" class="form-label fw-bold">Country</label>
                                    <input type="text" class="form-control" id="country" name="country" 
                                           value="Nigeria" readonly>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-dark">
                                <i class="fas fa-save me-2"></i>Update Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Shop Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Approval Status</label>
                        <div>
                            @if(auth()->user()->vendor->is_approved)
                                <span class="badge bg-success">
                                    <i class="fas fa-check-circle me-1"></i>Approved
                                </span>
                            @else
                                <span class="badge bg-warning">
                                    <i class="fas fa-clock me-1"></i>Pending Approval
                                </span>
                            @endif
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Featured Status</label>
                        <div>
                            @if(auth()->user()->vendor->is_featured)
                                <span class="badge bg-primary">
                                    <i class="fas fa-star me-1"></i>Featured
                                </span>
                            @else
                                <span class="badge bg-secondary">
                                    <i class="fas fa-star-o me-1"></i>Not Featured
                                </span>
                            @endif
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Subscription Status</label>
                        <div>
                            @if(auth()->user()->vendor->hasActiveSubscription())
                                <span class="badge bg-success">
                                    <i class="fas fa-crown me-1"></i>Active
                                </span>
                            @else
                                <span class="badge bg-warning">
                                    <i class="fas fa-exclamation-triangle me-1"></i>Free Tier
                                </span>
                            @endif
                        </div>
                        <small class="text-muted">
                            Orders processed: {{ auth()->user()->vendor->orders_processed }}/{{ auth()->user()->vendor->free_order_limit }}
                        </small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Shop URL</label>
                        <div>
                            <a href="{{ route('vendors.storefront', auth()->user()->vendor->slug) }}" 
                               target="_blank" class="text-decoration-none">
                                {{ route('vendors.storefront', auth()->user()->vendor->slug) }}
                                <i class="fas fa-external-link-alt ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate slug from shop name
    const shopNameInput = document.getElementById('shop_name');
    const slugInput = document.getElementById('slug');
    
    shopNameInput.addEventListener('input', function() {
        const slug = this.value.toLowerCase()
            .replace(/[^a-z0-9 -]/g, '') // Remove invalid chars
            .replace(/\s+/g, '-') // Replace spaces with -
            .replace(/-+/g, '-') // Replace multiple - with single -
            .trim('-'); // Trim - from start and end
        
        slugInput.value = slug;
    });
});
</script>
@endpush
