<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Product;
use App\Models\Color;
use App\Models\Size;

class ProductVariant extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'color_id',
        'size_id',
        'sku',
        'price_adjustment',
        'stock_quantity',
        'image_path',
    ];

    protected $casts = [
        'price_adjustment' => 'decimal:2',
        'stock_quantity' => 'integer',
    ];

    /**
     * Get the product that owns the variant.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the color of the variant.
     */
    public function color()
    {
        return $this->belongsTo(Color::class);
    }

    /**
     * Get the size of the variant.
     */
    public function size()
    {
        return $this->belongsTo(Size::class);
    }

    /**
     * Accessor for the final price of the variant.
     */
    public function getPriceAttribute()
    {
        if ($this->product && isset($this->product->price)) {
            return $this->product->price + ($this->price_adjustment ?? 0);
        }
        return $this->price_adjustment ?? 0; // Or handle as an error/default
    }

    /**
     * Accessor for the variant's image URL.
     * Falls back to product's main image if variant image is not set.
     */
    public function getImageUrlAttribute()
    {
        if ($this->image_path) {
            return asset('storage/' . $this->image_path);
        }
        if ($this->product && $this->product->image_url) {
            return $this->product->image_url; // Assuming image_url is an accessor on Product model
        }
        return asset('images/default-product.png');
    }
}
