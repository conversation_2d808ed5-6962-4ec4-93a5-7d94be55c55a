<?php

namespace App\Services;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Commission;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CommissionService
{
    /**
     * Calculate and record commission for an order
     *
     * @param Order $order
     * @return void
     */
    public function calculateOrderCommission(Order $order): void
    {
        try {
            DB::transaction(function () use ($order) {
                foreach ($order->items as $item) {
                    $this->calculateItemCommission($item);
                }
            });
        } catch (\Exception $e) {
            Log::error('Commission calculation failed for order: ' . $order->order_number, [
                'error' => $e->getMessage(),
                'order_id' => $order->id
            ]);
        }
    }

    /**
     * Calculate commission for a single order item
     *
     * @param OrderItem $item
     * @return void
     */
    public function calculateItemCommission(OrderItem $item): void
    {
        $commissionRate = $this->getCommissionRate();
        $itemTotal = $item->price_at_purchase * $item->quantity;
        $commissionAmount = $itemTotal * $commissionRate;

        // Update the order item with commission details
        $item->update([
            'commission_rate_snapshot' => $commissionRate,
            'commission_amount_calculated' => $commissionAmount
        ]);

        // Create or update commission record
        $this->recordCommission($item, $commissionAmount);
    }

    /**
     * Record commission in the commissions table
     *
     * @param OrderItem $item
     * @param float $amount
     * @return void
     */
    protected function recordCommission(OrderItem $item, float $amount): void
    {
        Commission::updateOrCreate(
            [
                'order_id' => $item->order_id,
                'order_item_id' => $item->id,
                'vendor_id' => $item->vendor_id
            ],
            [
                'amount' => $amount,
                'rate' => $item->commission_rate_snapshot,
                'status' => 'pending',
                'calculated_at' => now()
            ]
        );
    }

    /**
     * Get the current commission rate
     *
     * @return float
     */
    public function getCommissionRate(): float
    {
        return config('ecommerce.commission.rate', 0.027);
    }

    /**
     * Get total commission for a vendor
     *
     * @param int $vendorId
     * @param string $status
     * @return float
     */
    public function getVendorCommissionTotal(int $vendorId, string $status = null): float
    {
        $query = Commission::where('vendor_id', $vendorId);
        
        if ($status) {
            $query->where('status', $status);
        }
        
        return $query->sum('amount');
    }

    /**
     * Get commission summary for a vendor
     *
     * @param int $vendorId
     * @return array
     */
    public function getVendorCommissionSummary(int $vendorId): array
    {
        return [
            'total' => $this->getVendorCommissionTotal($vendorId),
            'pending' => $this->getVendorCommissionTotal($vendorId, 'pending'),
            'paid' => $this->getVendorCommissionTotal($vendorId, 'paid'),
            'withheld' => $this->getVendorCommissionTotal($vendorId, 'withheld')
        ];
    }

    /**
     * Mark commission as paid
     *
     * @param int $commissionId
     * @return bool
     */
    public function markCommissionAsPaid(int $commissionId): bool
    {
        try {
            $commission = Commission::findOrFail($commissionId);
            $commission->update([
                'status' => 'paid',
                'paid_at' => now()
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to mark commission as paid', [
                'commission_id' => $commissionId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Get platform commission earnings summary
     *
     * @param string $period
     * @return array
     */
    public function getPlatformCommissionSummary(string $period = 'all'): array
    {
        $query = Commission::query();
        
        switch ($period) {
            case 'today':
                $query->whereDate('created_at', today());
                break;
            case 'week':
                $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                break;
            case 'month':
                $query->whereMonth('created_at', now()->month)
                      ->whereYear('created_at', now()->year);
                break;
            case 'year':
                $query->whereYear('created_at', now()->year);
                break;
        }
        
        return [
            'total_commission' => $query->sum('amount'),
            'commission_count' => $query->count(),
            'average_commission' => $query->avg('amount'),
            'period' => $period
        ];
    }

    /**
     * Calculate commission for subscription payments
     *
     * @param float $amount
     * @return float
     */
    public function calculateSubscriptionCommission(float $amount): float
    {
        // For subscription payments, platform keeps 100%
        return $amount;
    }

    /**
     * Get commission rate as percentage string
     *
     * @return string
     */
    public function getCommissionRatePercentage(): string
    {
        return number_format($this->getCommissionRate() * 100, 2) . '%';
    }

    /**
     * Validate commission calculation
     *
     * @param OrderItem $item
     * @return bool
     */
    public function validateCommissionCalculation(OrderItem $item): bool
    {
        $expectedCommission = ($item->price_at_purchase * $item->quantity) * $item->commission_rate_snapshot;
        $actualCommission = $item->commission_amount_calculated;
        
        // Allow for small floating point differences
        return abs($expectedCommission - $actualCommission) < 0.01;
    }
}
