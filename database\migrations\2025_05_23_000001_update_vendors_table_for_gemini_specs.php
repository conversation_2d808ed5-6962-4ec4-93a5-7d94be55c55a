<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
            // Add missing fields according to gemini.md specifications
            if (!Schema::hasColumn('vendors', 'business_name')) {
                $table->string('business_name')->nullable()->after('user_id');
            }

            if (!Schema::hasColumn('vendors', 'address_line1')) {
                $table->string('address_line1')->nullable()->after('business_name');
            }

            if (!Schema::hasColumn('vendors', 'contact_phone')) {
                $table->string('contact_phone')->nullable()->after('state');
            }

            if (!Schema::hasColumn('vendors', 'contact_email_business')) {
                $table->string('contact_email_business')->nullable()->after('contact_phone');
            }

            if (!Schema::hasColumn('vendors', 'delivery_zone_type')) {
                $table->enum('delivery_zone_type', ['platform_delivery_zone', 'other_states_zone'])
                      ->nullable()
                      ->after('contact_email_business');
            }

            if (!Schema::hasColumn('vendors', 'subscription_status')) {
                $table->enum('subscription_status', ['active', 'inactive', 'pending_payment', 'expired'])
                      ->default('pending_payment')
                      ->after('delivery_zone_type');
            }

            // Rename is_approved to approved for consistency with specs
            if (Schema::hasColumn('vendors', 'is_approved') && !Schema::hasColumn('vendors', 'approved')) {
                $table->renameColumn('is_approved', 'approved');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
            $table->dropColumn([
                'business_name',
                'address_line1',
                'contact_phone',
                'contact_email_business',
                'delivery_zone_type',
                'subscription_status'
            ]);

            if (Schema::hasColumn('vendors', 'approved') && !Schema::hasColumn('vendors', 'is_approved')) {
                $table->renameColumn('approved', 'is_approved');
            }
        });
    }
};
