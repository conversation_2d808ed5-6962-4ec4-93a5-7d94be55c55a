<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remove brand_id from products table
        if (Schema::hasColumn('products', 'brand_id')) {
            Schema::table('products', function (Blueprint $table) {
                $table->dropForeign(['brand_id']);
                $table->dropColumn('brand_id');
            });
        }

        // Add brand-related fields to vendors table if they don't exist
        Schema::table('vendors', function (Blueprint $table) {
            if (!Schema::hasColumn('vendors', 'brand_description')) {
                $table->text('brand_description')->nullable()->after('description');
            }
            if (!Schema::hasColumn('vendors', 'brand_logo')) {
                $table->string('brand_logo')->nullable()->after('logo');
            }
        });

        // Drop the brands table entirely
        Schema::dropIfExists('brands');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate brands table
        Schema::create('brands', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('logo')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->timestamps();
        });

        // Add brand_id back to products table
        Schema::table('products', function (Blueprint $table) {
            $table->foreignId('brand_id')->nullable()->after('category_id')->constrained()->nullOnDelete();
        });

        // Remove brand-related fields from vendors table
        Schema::table('vendors', function (Blueprint $table) {
            if (Schema::hasColumn('vendors', 'brand_description')) {
                $table->dropColumn('brand_description');
            }
            if (Schema::hasColumn('vendors', 'brand_logo')) {
                $table->dropColumn('brand_logo');
            }
        });
    }
};
