<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">
    <head>
        @include('partials.head-bootstrap')
        <style>
            /* Additional auth-specific styles */
            .auth-container {
                min-height: 100vh;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 1.5rem;
            }
            .auth-card {
                width: 100%;
                max-width: 400px;
                background-color: #fff;
                border-radius: 0.5rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                padding: 2rem;
            }
            .dark .auth-card {
                background-color: #1a1a1a;
                color: #fff;
                border: 1px solid #333;
            }
            .auth-logo {
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-bottom: 1.5rem;
            }
            .auth-logo-icon {
                height: 2.25rem;
                width: 2.25rem;
                margin-bottom: 0.25rem;
            }
            .auth-form {
                display: flex;
                flex-direction: column;
                gap: 1.5rem;
            }
            .form-group {
                margin-bottom: 1rem;
            }
            .form-label {
                display: block;
                margin-bottom: 0.5rem;
                font-weight: 500;
            }
            .form-control {
                width: 100%;
                padding: 0.75rem;
                border-radius: 0.375rem;
                border: 1px solid #d1d5db;
            }
            .dark .form-control {
                background-color: #333;
                border-color: #555;
                color: #fff;
            }
            .form-control:focus {
                outline: none;
                border-color: #000;
                box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
            }
            .dark .form-control:focus {
                border-color: #fff;
                box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
            }
            .btn-primary {
                background-color: #000;
                color: #fff;
                border: none;
                padding: 0.75rem 1rem;
                border-radius: 0.375rem;
                font-weight: 500;
                cursor: pointer;
                transition: background-color 0.2s;
            }
            .btn-primary:hover {
                background-color: #333;
            }
            .dark .btn-primary {
                background-color: #fff;
                color: #000;
            }
            .dark .btn-primary:hover {
                background-color: #e5e5e5;
            }
            .text-center {
                text-align: center;
            }
            .text-sm {
                font-size: 0.875rem;
            }
            .text-muted {
                color: #6b7280;
            }
            .dark .text-muted {
                color: #9ca3af;
            }
            .auth-link {
                color: #000;
                text-decoration: none;
                font-weight: 500;
            }
            .auth-link:hover {
                text-decoration: underline;
            }
            .dark .auth-link {
                color: #fff;
            }
        </style>
    </head>
    <body class="min-h-screen bg-white antialiased dark:bg-neutral-900">
        <div class="auth-container">
            <div class="auth-card">
                <div class="auth-logo">
                    <a href="{{ route('home') }}" class="d-flex flex-column align-items-center">
                        <span class="auth-logo-icon d-flex align-items-center justify-content-center">
                            <x-app-logo-icon class="size-9 fill-current text-black dark:text-white" />
                        </span>
                        <span class="sr-only">{{ config('app.name', 'Laravel') }}</span>
                    </a>
                </div>
                {{ $slot }}
            </div>
        </div>
        @fluxScripts
    </body>
</html>
