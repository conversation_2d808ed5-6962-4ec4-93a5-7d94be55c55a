<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Subscription;
use App\Models\User;
use App\Models\SubscriptionPlan;

class SubscriptionController extends Controller
{
    public function index()
    {
        $subscriptions = Subscription::with(['user', 'plan'])->paginate(15);
        return view('admin.subscriptions.index', compact('subscriptions'));
    }
    public function edit(Subscription $subscription)
    {
        $plans = SubscriptionPlan::all();
        return view('admin.subscriptions.edit', compact('subscription', 'plans'));
    }
    public function update(Request $request, Subscription $subscription)
    {
        $data = $request->validate([
            'plan_id' => 'required|exists:subscription_plans,id',
            'status' => 'required',
            'end_date' => 'required|date',
        ]);
        $subscription->update($data);
        return redirect()->route('admin.subscriptions.index')->with('success', 'Subscription updated successfully.');
    }
}
