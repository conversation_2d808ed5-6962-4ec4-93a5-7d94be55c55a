@extends('layouts.app')

@section('content')
<div class="container-fluid py-5 px-3 px-sm-4 px-md-5">
    <div class="shop-header text-center mb-5 py-4 rounded-4 bg-light">
        <h1 class="shop-title display-4 fw-bold">Shop Our Products</h1>
        <p class="lead text-muted">Find the best products curated just for you.</p>
    </div>

    <div class="row">
        <!-- Mobile Filter Toggle Button (visible only on mobile) -->
        <div class="col-12 d-lg-none mb-3">
            <button class="btn btn-outline-dark w-100" type="button" data-bs-toggle="offcanvas" data-bs-target="#filterOffcanvas">
                <i class="fas fa-filter me-2"></i> Filter Products
            </button>
        </div>

        <!-- Filter Sidebar (visible only on desktop) -->
        <div class="col-lg-3 mb-4 mb-lg-0 d-none d-lg-block">
            <div class="filter-sidebar sticky-top" style="top: 100px;">
                <h5 class="mb-3">Filters</h5>
                <hr class="my-3">
                
                <!-- Category Filter Placeholder -->
                <div class="mb-4">
                    <h6>Categories</h6>
                    @if(isset($categories) && $categories->count())
                        <ul class="list-unstyled">
                            @foreach($categories as $category)
                                <li>
                                    <a href="{{ route('products.category', $category->slug) }}" 
                                       class="text-decoration-none {{ request()->is('shop/'.$category->slug) ? 'text-primary fw-bold' : 'text-dark' }}">
                                        {{ $category->name }}
                                    </a>
                                </li>
                            @endforeach
                        </ul>
                    @else
                        <p class="small text-muted">No categories available.</p>
                    @endif
                </div>
                <hr class="my-3">

                <!-- Price Filter Placeholder -->
                <div class="mb-4">
                    <h6>Price Range</h6>
                    <form action="{{ route('products.index') }}" method="GET" id="priceFilterForm">
                        {{-- Conditional hidden inputs for query and categories temporarily removed for debugging --}}
                        <div class="row g-2">
                            <div class="col">
                                <input type="number" name="min_price" class="form-control form-control-sm" placeholder="Min ₦" value="{{ request('min_price') }}">
                            </div>
                            <div class="col">
                                <input type="number" name="max_price" class="form-control form-control-sm" placeholder="Max ₦" value="{{ request('max_price') }}">
                            </div>
                        </div>
                        <button type="submit" class="btn btn-outline-dark btn-sm w-100 mt-2">Apply Price</button>
                    </form>
                </div>
                <hr class="my-3">

                <!-- Sorting Placeholder -->
                <div class="mb-4">
                    <h6>Sort By</h6>
                     <form action="{{ route('products.index') }}" method="GET" id="sortForm">
                        {{-- Conditional hidden inputs for query, categories, and price temporarily removed for debugging --}}
                        <select name="sort_by" class="form-select form-select-sm" onchange="document.getElementById('sortForm').submit();">
                            <option value="latest" {{ request('sort_by') == 'latest' ? 'selected' : '' }}>Latest</option>
                            <option value="price_asc" {{ request('sort_by') == 'price_asc' ? 'selected' : '' }}>Price: Low to High</option>
                            <option value="price_desc" {{ request('sort_by') == 'price_desc' ? 'selected' : '' }}>Price: High to Low</option>
                            <option value="name_asc" {{ request('sort_by') == 'name_asc' ? 'selected' : '' }}>Name: A to Z</option>
                            <option value="name_desc" {{ request('sort_by') == 'name_desc' ? 'selected' : '' }}>Name: Z to A</option>
                        </select>
                    </form>
                </div>
                <hr class="my-3">
                <a href="{{ route('products.index') }}" class="btn btn-sm btn-outline-secondary w-100">Clear All Filters</a>

            </div>
        </div>

        <!-- Mobile Filter Offcanvas (visible only when triggered on mobile) -->
        <div class="offcanvas offcanvas-start" tabindex="-1" id="filterOffcanvas" aria-labelledby="filterOffcanvasLabel">
            <div class="offcanvas-header">
                <h5 class="offcanvas-title" id="filterOffcanvasLabel">Filter Products</h5>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body">
                <div class="filter-sidebar">
                    <h5 class="mb-3">Filters</h5>
                    <hr class="my-3">
                    
                    <!-- Category Filter Placeholder -->
                    <div class="mb-4">
                        <h6>Categories</h6>
                        @if(isset($categories) && $categories->count())
                            <ul class="list-unstyled">
                                @foreach($categories as $category)
                                    <li>
                                        <a href="{{ route('products.category', $category->slug) }}" 
                                           class="text-decoration-none {{ request()->is('shop/'.$category->slug) ? 'text-primary fw-bold' : 'text-dark' }}">
                                            {{ $category->name }}
                                        </a>
                                    </li>
                                @endforeach
                            </ul>
                        @else
                            <p class="small text-muted">No categories available.</p>
                        @endif
                    </div>
                    <hr class="my-3">

                    <!-- Price Filter Placeholder -->
                    <div class="mb-4">
                        <h6>Price Range</h6>
                        <form action="{{ route('products.index') }}" method="GET" id="mobilePriceFilterForm">
                            <div class="row g-2">
                                <div class="col">
                                    <input type="number" name="min_price" class="form-control form-control-sm" placeholder="Min ₦" value="{{ request('min_price') }}">
                                </div>
                                <div class="col">
                                    <input type="number" name="max_price" class="form-control form-control-sm" placeholder="Max ₦" value="{{ request('max_price') }}">
                                </div>
                            </div>
                            <button type="submit" class="btn btn-outline-dark btn-sm w-100 mt-2">Apply Price</button>
                        </form>
                    </div>
                    <hr class="my-3">

                    <!-- Sorting Placeholder -->
                    <div class="mb-4">
                        <h6>Sort By</h6>
                         <form action="{{ route('products.index') }}" method="GET" id="mobileSortForm">
                            <select name="sort_by" class="form-select form-select-sm" onchange="document.getElementById('mobileSortForm').submit();">
                                <option value="latest" {{ request('sort_by') == 'latest' ? 'selected' : '' }}>Latest</option>
                                <option value="price_asc" {{ request('sort_by') == 'price_asc' ? 'selected' : '' }}>Price: Low to High</option>
                                <option value="price_desc" {{ request('sort_by') == 'price_desc' ? 'selected' : '' }}>Price: High to Low</option>
                                <option value="name_asc" {{ request('sort_by') == 'name_asc' ? 'selected' : '' }}>Name: A to Z</option>
                                <option value="name_desc" {{ request('sort_by') == 'name_desc' ? 'selected' : '' }}>Name: Z to A</option>
                            </select>
                        </form>
                    </div>
                    <hr class="my-3">
                    <a href="{{ route('products.index') }}" class="btn btn-sm btn-outline-secondary w-100">Clear All Filters</a>
                </div>
            </div>
        </div>

        <!-- Products Grid -->
        <div class="col-lg-9">
            <div class="d-flex justify-content-between align-items-center flex-wrap mb-4">
                <div>
                    <span class="text-muted">Showing {{ $products->firstItem() ?? 0 }} - {{ $products->lastItem() ?? 0 }} of {{ $products->total() ?? 0 }} products</span>
                </div>
                
                <div class="d-none d-md-block">
                    <form action="{{ route('products.index') }}" method="GET" class="d-flex" id="desktopSortForm">
                        <label for="sort_by" class="me-2 align-self-center">Sort by:</label>
                        <select name="sort_by" id="sort_by" class="form-select form-select-sm" style="width: auto;" onchange="document.getElementById('desktopSortForm').submit();">
                            <option value="latest" {{ request('sort_by') == 'latest' ? 'selected' : '' }}>Latest</option>
                            <option value="price_asc" {{ request('sort_by') == 'price_asc' ? 'selected' : '' }}>Price: Low to High</option>
                            <option value="price_desc" {{ request('sort_by') == 'price_desc' ? 'selected' : '' }}>Price: High to Low</option>
                            <option value="name_asc" {{ request('sort_by') == 'name_asc' ? 'selected' : '' }}>Name: A to Z</option>
                            <option value="name_desc" {{ request('sort_by') == 'name_desc' ? 'selected' : '' }}>Name: Z to A</option>
                        </select>
                    </form>
                </div>
            </div>
            <form action="{{ route('products.search') }}" method="GET" class="mb-4">
                <div class="input-group">
                    <input type="text" name="query" class="form-control" placeholder="Search products..." value="{{ request('query') }}">
                    <button class="btn btn-dark" type="submit"><i class="fas fa-search"></i></button>
                </div>
            </form>

            <div id="product-grid-container">
                @include('products._product_grid', ['products' => $products])
            </div>

        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    /* General */
    body {
        /* Assuming a white or very light gray background is set globally */
    }

    a {
        color: #333; /* Dark gray for links */
        text-decoration: none;
    }

    a:hover {
        color: #000; /* Black on hover */
        text-decoration: underline;
    }

    /* Shop Header */
    .shop-header .shop-title {
        color: #000;
        font-weight: 600;
    }
    .shop-header .lead {
        color: #555;
    }

    /* Filter Sidebar */
    .filter-sidebar {
        background-color: #f8f9fa; /* Very light gray background for contrast */
        padding: 20px;
        border-radius: 0.25rem;
        border: 1px solid #eee;
    }
    .filter-sidebar h5, .filter-sidebar h6 {
        color: #000;
        font-weight: 600;
        margin-bottom: 0.75rem;
    }
    .filter-sidebar .list-group-item {
        border: none; /* Cleaner look for list items */
        padding: 0.5rem 0;
        background-color: transparent;
    }
    .filter-sidebar .list-group-item a {
        color: #333;
    }
    .filter-sidebar .list-group-item a:hover,
    .filter-sidebar .list-group-item.active a {
        color: #000;
        font-weight: 500;
    }
    .filter-sidebar .list-group-item.active a {
        text-decoration: underline;
    }

    .filter-sidebar .form-control-sm,
    .filter-sidebar .form-select-sm {
        border-color: #ccc;
    }
    .filter-sidebar .form-control-sm:focus,
    .filter-sidebar .form-select-sm:focus {
        border-color: #000;
        box-shadow: none;
    }
    .filter-sidebar .btn-outline-dark:hover {
        background-color: #000;
        color: #fff;
    }
    .filter-sidebar .btn-outline-secondary {
        border-color: #6c757d;
        color: #6c757d;
    }
    .filter-sidebar .btn-outline-secondary:hover {
        background-color: #6c757d;
        color: #fff;
    }

    /* Product Cards */
    .product-card {
        border: 1px solid #eee; /* Subtle border */
        transition: box-shadow 0.3s ease-in-out;
        background-color: #fff;
    }
    .product-card:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    .product-card .card-img-top {
        aspect-ratio: 1 / 1; /* Maintain square aspect ratio */
        object-fit: cover;
    }
    .product-card .card-title a {
        color: #212529; /* Bootstrap's default dark */
        font-weight: 500;
    }
    .product-card .card-title a:hover {
        color: #000 !important; /* Override if necessary, ensure black */
        text-decoration: none;
    }
    .product-card .card-text.text-muted a {
        color: #6c757d; /* Bootstrap's default muted */
    }
    .product-card .card-text.text-muted a:hover {
        color: #333;
        text-decoration: underline;
    }
    .product-card .fw-bold.fs-5 {
        color: #000 !important; /* Price color to black */
    }
    .product-card .btn-dark {
        background-color: #212529;
        border-color: #212529;
    }
    .product-card .btn-dark:hover {
        background-color: #000;
        border-color: #000;
    }

    /* Ensure stretched link doesn't interfere with other hovers */
    .stretched-link::after {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 1;
        content: "";
    }

    /* Pagination (ensure B&W theme) */
    .pagination .page-item .page-link {
        color: #333; /* Dark gray for page links */
        border-color: #ddd;
    }
    .pagination .page-item .page-link:hover {
        color: #000;
        background-color: #e9ecef; /* Light gray hover */
        border-color: #ccc;
    }
    .pagination .page-item.active .page-link {
        z-index: 3;
        color: #fff; /* White text on active */
        background-color: #000; /* Black background for active */
        border-color: #000;
    }
    .pagination .page-item.disabled .page-link {
        color: #6c757d;
        background-color: #fff;
        border-color: #ddd;
    }

</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function () {
    const productGridContainer = document.getElementById('product-grid-container');
    const filterSidebar = document.querySelector('.filter-sidebar');
    const initialUrl = window.location.href;

    function showLoading() {
        // Simple loading state: you can replace this with a more sophisticated spinner
        if (productGridContainer) {
            productGridContainer.innerHTML = '<div class="d-flex justify-content-center align-items-center" style="min-height: 300px;"><div class="spinner-border text-dark" role="status"><span class="visually-hidden">Loading...</span></div></div>';
        }
    }

    function fetchProducts(url, pushStateToHistory = true) {
        showLoading();

        fetch(url, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest', // Important for Laravel to detect AJAX
                'Accept': 'application/json',
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.statusText);
            }
            return response.json();
        })
        .then(data => {
            if (productGridContainer && data.html_content) {
                productGridContainer.innerHTML = data.html_content;
                // Re-initialize add-to-cart listeners for new products
                initializeAjaxAddToCart(); 
                // Re-initialize pagination listeners
                initializePaginationListeners();
            }
            if (pushStateToHistory && data.query_params) {
                const newUrl = new URL(window.location.origin + window.location.pathname);
                for (const key in data.query_params) {
                    if (data.query_params[key] !== null && data.query_params[key] !== '') {
                        newUrl.searchParams.set(key, data.query_params[key]);
                    }
                }
                // Only push state if URL actually changes to avoid cluttering history
                if (newUrl.href !== window.location.href) {
                    history.pushState({ path: newUrl.href }, '', newUrl.href);
                }
            }
        })
        .catch(error => {
            console.error('Error fetching products:', error);
            if (productGridContainer) {
                productGridContainer.innerHTML = '<div class="alert alert-danger">Could not load products. Please try again.</div>';
            }
        });
    }

    // Event Listeners for Filters
    if (filterSidebar) {
        // Category Links (assuming they are <a> tags with hrefs)
        filterSidebar.addEventListener('click', function(e) {
            if (e.target.matches('.list-group-item a, .category-filter-link')) { // Adjust selector if needed
                e.preventDefault();
                const url = e.target.href;
                fetchProducts(url);
            }
        });

        // Price Filter Form
        const priceFilterForm = document.getElementById('priceFilterForm');
        if (priceFilterForm) {
            priceFilterForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const formData = new FormData(priceFilterForm);
                const params = new URLSearchParams(formData);
                // Append existing query params like search query or sort_by if they are not part of this form
                const currentParams = new URLSearchParams(window.location.search);
                if (currentParams.has('query')) params.set('query', currentParams.get('query'));
                if (currentParams.has('sort_by') && !formData.has('sort_by')) params.set('sort_by', currentParams.get('sort_by'));
                
                const url = `${priceFilterForm.action}?${params.toString()}`;
                fetchProducts(url);
            });
        }

        // Sort By Select
        const sortForm = document.getElementById('sortForm');
        const sortBySelect = sortForm ? sortForm.querySelector('select[name="sort_by"]') : null;
        if (sortBySelect) {
            sortBySelect.addEventListener('change', function(e) {
                const formData = new FormData(sortForm);
                const params = new URLSearchParams(formData);
                // Append existing query params like search query or category if they are not part of this form
                const currentParams = new URLSearchParams(window.location.search);
                if (currentParams.has('query')) params.set('query', currentParams.get('query'));
                if (currentParams.has('category') && !formData.has('category')) params.set('category', currentParams.get('category'));
                if (currentParams.has('min_price') && !formData.has('min_price')) params.set('min_price', currentParams.get('min_price'));
                if (currentParams.has('max_price') && !formData.has('max_price')) params.set('max_price', currentParams.get('max_price'));
                
                const url = `${sortForm.action}?${params.toString()}`;
                fetchProducts(url);
            });
        }

        // Clear All Filters Link
        const clearFiltersLink = filterSidebar.querySelector('a[href="{{ route('products.index') }}"]');
        if (clearFiltersLink && clearFiltersLink.classList.contains('btn-outline-secondary')) { // Be more specific if needed
             clearFiltersLink.addEventListener('click', function(e){
                e.preventDefault();
                fetchProducts(this.href);
             });       
        }
    }

    // Pagination Links (needs to be initialized and re-initialized after content update)
    function initializePaginationListeners() {
        if (productGridContainer) {
            productGridContainer.addEventListener('click', function(e) {
                // Check if the clicked element or its parent is a pagination link
                let targetLink = e.target.closest('.pagination a.page-link');
                if (targetLink) {
                    e.preventDefault();
                    const url = targetLink.href;
                    if (url && url !== '#') { // Ensure it's a valid URL
                        fetchProducts(url);
                    }
                }
            });
        }
    }

    // Handle Back/Forward browser navigation
    window.addEventListener('popstate', function(e) {
        if (e.state && e.state.path) {
            fetchProducts(e.state.path, false); // false: don't push to history again
        } else {
            // If no state, it might be the initial page or a state we didn't set
            // For simplicity, you could reload or fetch based on current URL
            fetchProducts(initialUrl, false); 
        }
    });

    // Initializers
    initializePaginationListeners();
    initializeAjaxAddToCart(); // Call the existing add to cart function

    // Existing AJAX add to cart (ensure it's defined or move it here)
    // Assuming initializeAjaxAddToCart is defined elsewhere or copying its logic here:
    function initializeAjaxAddToCart() {
        const cartForms = document.querySelectorAll('.ajax-add-to-cart-form');
        cartForms.forEach(form => {
            // Prevent re-attaching listeners if already done
            if (form.dataset.ajaxListenerAttached) return;
            form.dataset.ajaxListenerAttached = 'true';

            form.addEventListener('submit', function (e) {
                e.preventDefault();
                const formData = new FormData(form);
                const action = form.getAttribute('action');
                const button = form.querySelector('.add-to-cart-btn');
                const originalButtonText = button.innerHTML;
                button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Adding...';
                button.disabled = true;

                fetch(action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const cartCountBadge = document.querySelector('.cart-count-badge');
                        if (cartCountBadge) {
                            cartCountBadge.textContent = data.cart_count;
                            cartCountBadge.classList.add('animate__animated', 'animate__bounceIn');
                            setTimeout(() => cartCountBadge.classList.remove('animate__animated', 'animate__bounceIn'), 1000);
                        }
                        button.innerHTML = '<i class="fas fa-check"></i> Added!';
                        setTimeout(() => {
                            button.innerHTML = originalButtonText;
                            button.disabled = false;
                        }, 2000);
                    } else {
                        alert(data.message || 'Could not add product to cart.');
                        button.innerHTML = originalButtonText;
                        button.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                    button.innerHTML = originalButtonText;
                    button.disabled = false;
                });
            });
        });
    }
});
</script>
@endpush