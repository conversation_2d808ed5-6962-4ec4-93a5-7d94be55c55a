<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'Brandify') }}</title>
    <!-- Black and White Theme CSS -->
    @include('partials.head-bw')
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white fixed-top shadow-sm py-3">
        <div class="container">
            <a class="navbar-brand fw-bold d-flex align-items-center" href="{{ route('home') }}">
                <img src="{{ asset('storage/brandify.png') }}" alt="Brandify Logo" class="me-2"
                    style="height: 30px; width: auto;">
                <span class="fs-4">{{ config('app.name', 'Brandify') }}</span>
            </a>
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item">
                        <a class="nav-link mx-3 fw-medium" href="{{ route('home') }}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link mx-3 fw-medium" href="{{ route('products.index') }}">Shop</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link mx-3 fw-medium" href="{{ route('about') }}">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link mx-3 fw-medium" href="{{ route('contact') }}">Contact</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center gap-4">
                    @auth
                        <div class="dropdown">
                            <a href="#" class="text-dark position-relative dropdown-toggle" id="userDropdown"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fa-regular fa-user fs-5"></i>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><span class="dropdown-item-text">{{ auth()->user()->name }}</span></li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                @if (auth()->user()->isAdmin())
                                    <li><a class="dropdown-item" href="{{ route('admin.dashboard') }}">Admin Dashboard</a>
                                    </li>
                                @endif
                                @if (auth()->user()->isVendor())
                                    <li><a class="dropdown-item" href="{{ route('vendor.dashboard') }}">Vendor
                                            Dashboard</a>
                                    </li>
                                @endif
                                <li><a class="dropdown-item" href="{{ route('dashboard') }}">My Account</a></li>
                                <li><a class="dropdown-item" href="{{ route('wishlist.index') }}">My Wishlist</a></li>
                                <li><a class="dropdown-item" href="{{ route('orders.index') }}">My Orders</a></li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <li>
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit" class="dropdown-item">Logout</button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    @else
                        <a href="{{ route('login') }}" class="text-dark position-relative" data-bs-toggle="tooltip"
                            title="Login">
                            <i class="fa-regular fa-user fs-5"></i>
                        </a>
                    @endauth

                    <a href="{{ route('cart.index') }}" class="text-dark position-relative" data-bs-toggle="tooltip"
                        title="Cart">
                        <i class="fa-solid fa-cart-shopping fs-5"></i>
                        <span
                            class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-dark cart-count-badge">
                            <?php $cartCount = collect(session('cart', []))->sum('quantity'); ?>
                            {{ $cartCount }}
                        </span>
                    </a>

                    @auth
                        <a href="{{ route('wishlist.index') }}" class="text-dark position-relative"
                            data-bs-toggle="tooltip" title="Wishlist">
                            <i class="fa-regular fa-heart fs-5"></i>
                        </a>
                    @endauth
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid pt-5 mt-5 px-3 px-sm-4 px-md-5">
        <!-- Search Bar -->
        <div class="row justify-content-center my-4">
            <div class="col-md-8">
                <form action="{{ route('products.search') }}" method="GET" class="search-form">
                    <div class="input-group">
                        <span class="input-group-text bg-white border-end-0">
                            <i class="fa-solid fa-magnifying-glass text-muted"></i>
                        </span>
                        <input type="text" name="query" class="form-control border-start-0"
                            placeholder="Search for brands, products, and more..." required>
                        <button class="btn btn-dark" type="submit">
                            <span class="d-none d-md-inline">Search</span>
                            <i class="fa-solid fa-search d-inline d-md-none"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Hero Section -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="rounded-4 overflow-hidden bg-dark position-relative shadow">
                    <div class="row g-0">
                        <div class="col-lg-6 p-4 p-md-5 d-flex flex-column justify-content-center order-2 order-lg-1">
                            <span class="text-uppercase fw-medium text-white-50 mb-2">Welcome to Brandify</span>
                            <h1 class="display-5 fw-bold text-white mb-3">Premium Brands at Your Fingertips</h1>
                            <p class="text-light mb-4">Discover the latest collections from top designers around the
                                world. Experience luxury shopping redefined.</p>
                            <div class="d-flex flex-wrap gap-2">
                                <a href="{{ route('products.index') }}"
                                    class="btn btn-light px-4 fw-medium position-relative overflow-hidden">
                                    <span class="position-relative z-1">Shop Now</span>
                                </a>
                                <a href="{{ route('about') }}" class="btn btn-outline-light px-4 fw-medium">Learn
                                    More</a>
                            </div>
                        </div>
                        <div class="col-lg-6 order-1 order-lg-2">
                            <!-- Hero Slider -->
                            <div class="hero-slider position-relative"
                                style="background-image: url('{{ asset('storage/banner3.jpg') }}'); background-size: cover; background-repeat: no-repeat; background-position: center; background-color: var(--primary-color); height: 100%; min-height: 300px; min-height: 250px; max-height: 450px; transition: background-image 1s ease-in-out, opacity 1s ease-in-out;">

                                <div class="hero-overlay"></div>

                                <!-- Slider Navigation -->
                                <button type="button"
                                    class="slider-prev position-absolute top-50 start-0 translate-middle-y btn btn-sm btn-dark rounded-circle m-3"
                                    style="width: 40px; height: 40px; z-index: 10;">
                                    <i class="fa-solid fa-chevron-left"></i>
                                </button>
                                <button type="button"
                                    class="slider-next position-absolute top-50 end-0 translate-middle-y btn btn-sm btn-dark rounded-circle m-3"
                                    style="width: 40px; height: 40px; z-index: 10;">
                                    <i class="fa-solid fa-chevron-right"></i>
                                </button>

                                <!-- Slider Dots -->
                                <div class="position-absolute bottom-0 start-50 translate-middle-x mb-3 d-flex gap-2"
                                    style="z-index: 10;">
                                    @for ($i = 0; $i < 3; $i++)
                                        <button type="button"
                                            class="slider-dot btn btn-sm p-0 rounded-circle {{ $i === 0 ? 'active bg-dark' : 'bg-secondary' }}"
                                            style="width: 12px; height: 12px;" data-index="{{ $i }}"
                                            aria-label="Go to slide {{ $i + 1 }}">
                                        </button>
                                    @endfor
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Featured Vendors Section -->
        <div class="row mb-5">
            <div class="col-12 mb-4">
                <div
                    class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                    <div class="d-flex flex-column flex-md-row align-items-start align-items-md-center mb-3 mb-md-0">
                        <h2 class="fw-bold mb-0 me-0 me-md-3">Featured Vendors</h2>
                        <span class="text-muted">Premium clothing brands</span>
                    </div>
                    <a href="{{ route('products.index') }}"
                        class="text-dark text-decoration-none fw-medium d-flex align-items-center">
                        <span>View All</span>
                        <i class="fa-solid fa-arrow-right ms-2 transition-transform"></i>
                    </a>
                </div>
            </div>

            <!-- Vendor Cards (Each vendor is their own brand) -->
            @forelse($featuredVendors as $vendor)
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 border-0 hover-shadow">
                        <div class="card-body p-4">
                            <div class="d-flex justify-content-center align-items-center h-40 mb-4">
                                @if ($vendor->logo && file_exists(public_path('storage/' . $vendor->logo)))
                                    <img src="{{ asset('storage/' . $vendor->logo) }}"
                                        alt="{{ $vendor->shop_name }}" class="img-fluid" style="max-height: 60px;">
                                @else
                                    <img src="https://via.placeholder.com/200x60?text={{ urlencode($vendor->shop_name) }}"
                                        alt="{{ $vendor->shop_name }}" class="img-fluid" style="max-height: 60px;">
                                @endif
                            </div>
                            <h3 class="fw-bold mb-2">{{ $vendor->shop_name }}</h3>
                            <p class="text-muted mb-4 small">{{ Str::limit($vendor->description, 100) }}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-muted small">{{ $vendor->products_count ?? 0 }}+
                                    Products</span>
                                <a href="{{ route('vendors.storefront', $vendor->slug) }}"
                                    class="btn btn-sm btn-outline-dark">View Collection <i
                                        class="fas fa-arrow-right ms-1"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-12">
                    <div class="alert alert-light text-center" role="alert">
                        <i class="fa-regular fa-building fs-3 mb-2 d-block"></i>
                        <p class="mb-0">No featured vendors available at the moment. Check back soon!</p>
                    </div>
                </div>
            @endforelse
        </div>

        <!-- Products Section -->
        <div class="row mb-5" x-data="{ activeTab: 'new' }">
            <div class="col-12 mb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="fw-bold mb-0">Our Products</h2>
                    <a href="{{ route('products.index') }}"
                        class="text-dark text-decoration-none fw-medium d-flex align-items-center">
                        <span>View All</span>
                        <i class="fa-solid fa-arrow-right ms-2 transition-transform"></i>
                    </a>
                </div>
            </div>

            <!-- Tabs -->
            <div class="col-12 mb-4">
                <ul class="nav nav-tabs border-bottom">
                    <li class="nav-item">
                        <button @click="activeTab = 'new'" :class="{ 'active': activeTab === 'new' }"
                            class="nav-link">New Arrivals</button>
                    </li>
                    <li class="nav-item">
                        <button @click="activeTab = 'best'" :class="{ 'active': activeTab === 'best' }"
                            class="nav-link">Best Sellers</button>
                    </li>
                </ul>
            </div>

            <!-- New Arrivals Tab -->
            <div class="col-12" x-show="activeTab === 'new'">
                <div class="row">
                    <!-- Product Cards -->
                    @forelse($newArrivals as $product)
                        <div class="col-md-3 mb-4">
                            <div class="card h-100 border hover-border-dark">
                                <div class="position-relative">
                                    @if (
                                        $product->image_url &&
                                            (filter_var($product->image_url, FILTER_VALIDATE_URL) || file_exists(public_path($product->image_url))))
                                        <img src="{{ $product->image_url }}" alt="{{ $product->name }}"
                                            class="card-img-top" style="height: 200px; object-fit: cover;">
                                    @else
                                        <img src="https://via.placeholder.com/400x200?text={{ urlencode($product->name) }}"
                                            alt="{{ $product->name }}" class="card-img-top"
                                            style="height: 200px; object-fit: cover;">
                                    @endif
                                    <div
                                        class="position-absolute top-2 start-2 bg-black text-white text-xs fw-bold px-2 py-1 rounded">
                                        NEW
                                    </div>
                                    @if ($product->isOnSale())
                                        <div
                                            class="position-absolute top-2 end-2 bg-danger text-white text-xs fw-bold px-2 py-1 rounded">
                                            {{ $product->getDiscountPercentage() }}% OFF
                                        </div>
                                    @endif
                                </div>
                                <div class="card-body p-4">
                                    <p class="text-muted small mb-1">{{ $product->category->name }}</p>
                                    <h5 class="fw-semibold mb-1">
                                        <a href="{{ route('products.show', $product->slug) }}"
                                            class="text-decoration-none text-dark">
                                            {{ $product->name }}
                                        </a>
                                    </h5>
                                    @if ($product->isOnSale())
                                        <div class="d-flex align-items-center mb-3">
                                            <p class="fw-bold me-2 mb-0">
                                                ₦{{ number_format($product->discount_price, 2) }}</p>
                                            <p class="text-muted text-decoration-line-through mb-0">
                                                ₦{{ number_format($product->price, 2) }}</p>
                                        </div>
                                    @else
                                        <p class="fw-bold mb-3">₦{{ number_format($product->price, 2) }}</p>
                                    @endif
                                    <form action="{{ route('cart.add', ['product' => $product->id]) }}"
                                        method="POST">
                                        @csrf
                                        <button type="submit"
                                            class="btn btn-dark w-100 d-flex align-items-center justify-content-center">
                                            <i class="fa-solid fa-cart-plus me-2"></i> Add to Cart
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                No new products available at the moment.
                            </div>
                        </div>
                    @endforelse
                </div>
            </div>

            <!-- Best Sellers Tab -->
            <div class="col-12" x-show="activeTab === 'best'">
                <div class="row">
                    <!-- Product Cards -->
                    @forelse($featuredProducts as $product)
                        <div class="col-md-3 mb-4">
                            <div class="card h-100 border hover-border-dark">
                                <div class="position-relative">
                                    @if (
                                        $product->image_url &&
                                            (filter_var($product->image_url, FILTER_VALIDATE_URL) || file_exists(public_path($product->image_url))))
                                        <img src="{{ $product->image_url }}" alt="{{ $product->name }}"
                                            class="card-img-top" style="height: 200px; object-fit: cover;">
                                    @else
                                        <img src="https://via.placeholder.com/400x200?text={{ urlencode($product->name) }}"
                                            alt="{{ $product->name }}" class="card-img-top"
                                            style="height: 200px; object-fit: cover;">
                                    @endif
                                    <div
                                        class="position-absolute top-2 start-2 bg-black text-white text-xs fw-bold px-2 py-1 rounded">
                                        BEST SELLER
                                    </div>
                                    @if ($product->isOnSale())
                                        <div
                                            class="position-absolute top-2 end-2 bg-danger text-white text-xs fw-bold px-2 py-1 rounded">
                                            {{ $product->getDiscountPercentage() }}% OFF
                                        </div>
                                    @endif
                                </div>
                                <div class="card-body p-4">
                                    <p class="text-muted small mb-1">{{ $product->category->name }}</p>
                                    <h5 class="fw-semibold mb-1">
                                        <a href="{{ route('products.show', $product->slug) }}"
                                            class="text-decoration-none text-dark">
                                            {{ $product->name }}
                                        </a>
                                    </h5>
                                    @if ($product->isOnSale())
                                        <div class="d-flex align-items-center mb-3">
                                            <p class="fw-bold me-2 mb-0">
                                                ₦{{ number_format($product->discount_price, 2) }}</p>
                                            <p class="text-muted text-decoration-line-through mb-0">
                                                ₦{{ number_format($product->price, 2) }}</p>
                                        </div>
                                    @else
                                        <p class="fw-bold mb-3">₦{{ number_format($product->price, 2) }}</p>
                                    @endif
                                    <form action="{{ route('cart.add', ['product' => $product->id]) }}"
                                        method="POST">
                                        @csrf
                                        <button type="submit"
                                            class="btn btn-dark w-100 d-flex align-items-center justify-content-center">
                                            <i class="fa-solid fa-cart-plus me-2"></i> Add to Cart
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                No featured products available at the moment.
                            </div>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Newsletter Section -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="bg-light p-4 p-md-5 rounded-4 text-center shadow-sm">
                    <div class="row justify-content-center">
                        <div class="col-md-10 col-lg-8">
                            <h2 class="fw-bold mb-3">Join Our Newsletter</h2>
                            <p class="text-muted mb-4">Stay updated with the latest products, exclusive offers, and
                                fashion news.</p>
                            <form class="newsletter-form">
                                <div class="d-flex flex-column flex-sm-row gap-2">
                                    <input type="email" class="form-control p-3" placeholder="Your email address"
                                        required>
                                    <button class="btn btn-dark px-4 py-3 fw-semibold" type="submit">
                                        <span class="d-none d-md-inline">Subscribe</span>
                                        <i class="fas fa-paper-plane d-inline d-md-none"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 mb-4">
                    <h3 class="fw-bold mb-3">{{ config('app.name', 'Brandify') }}</h3>
                    <p class="text-muted mb-3">Your destination for premium brands and designer products.</p>
                    <div class="d-flex gap-3">
                        <a href="#"><i class="fa-brands fa-facebook-f"></i></a>
                        <a href="#"><i class="fa-brands fa-instagram"></i></a>
                        <a href="#"><i class="fa-brands fa-twitter"></i></a>
                    </div>
                </div>

                <div class="col-lg-3 mb-4">
                    <h5 class="fw-semibold mb-3">Shop</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="{{ route('products.index') }}">New Arrivals</a></li>
                        <li class="mb-2"><a href="{{ route('products.index') }}">Best Sellers</a></li>
                        <li class="mb-2"><a href="{{ route('products.index') }}">Brands</a></li>
                        <li class="mb-2"><a href="{{ route('products.index') }}">Sale</a></li>
                    </ul>
                </div>

                <div class="col-lg-3 mb-4">
                    <h5 class="fw-semibold mb-3">Help</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="#">Contact Us</a></li>
                        <li class="mb-2"><a href="#">FAQs</a></li>
                        <li class="mb-2"><a href="#">Shipping</a></li>
                        <li class="mb-2"><a href="#">Returns</a></li>
                    </ul>
                </div>

                <div class="col-lg-3 mb-4">
                    <h5 class="fw-semibold mb-3">About</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="#">Our Story</a></li>
                        <li class="mb-2"><a href="#">Careers</a></li>
                        <li class="mb-2"><a href="#">Privacy Policy</a></li>
                        <li class="mb-2"><a href="#">Terms of Service</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-top border-secondary pt-4 mt-4 text-center text-muted">
                <p>&copy; {{ date('Y') }} {{ config('app.name', 'Brandify') }}. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Hero Slider JavaScript -->
    <script src="{{ asset('js/slider-config.js') }}"></script>

    <!-- Cart AJAX JavaScript -->
    <script src="{{ asset('js/cart.js') }}"></script>
</body>

</html>
