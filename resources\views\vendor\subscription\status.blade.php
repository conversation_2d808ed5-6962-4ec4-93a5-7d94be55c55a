@extends('layouts.vendor')

@section('title', 'Subscription Status')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Subscription Status</h1>
                    <p class="text-muted">Manage your subscription and view usage details</p>
                </div>
                <a href="{{ route('vendor.dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
                </a>
            </div>

            <!-- Current Status Card -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Current Subscription Status</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Status</label>
                                        <div>
                                            @php
                                                $statusColors = [
                                                    'active' => 'success',
                                                    'pending' => 'warning',
                                                    'inactive' => 'secondary',
                                                    'cancelled' => 'danger',
                                                    'suspended' => 'dark'
                                                ];
                                                $color = $statusColors[$vendor->subscription_status] ?? 'secondary';
                                            @endphp
                                            <span class="badge bg-{{ $color }} fs-6">{{ ucfirst($vendor->subscription_status) }}</span>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Orders Processed</label>
                                        <div class="fs-4 fw-bold">{{ $subscriptionDetails['orders_processed'] }}</div>
                                    </div>
                                    
                                    @if(!$subscriptionDetails['needs_subscription'])
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Free Orders Remaining</label>
                                            <div class="fs-4 fw-bold text-success">{{ $subscriptionDetails['free_orders_remaining'] }}</div>
                                        </div>
                                    @endif
                                </div>
                                
                                <div class="col-md-6">
                                    @if($vendor->subscription_expires_at)
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Subscription Expires</label>
                                            <div class="fs-5">{{ $vendor->subscription_expires_at->format('M d, Y') }}</div>
                                            <small class="text-muted">{{ $vendor->subscription_expires_at->diffForHumans() }}</small>
                                        </div>
                                    @endif
                                    
                                    @if($vendor->subscription_started_at)
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Subscription Started</label>
                                            <div class="fs-6">{{ $vendor->subscription_started_at->format('M d, Y') }}</div>
                                        </div>
                                    @endif
                                    
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Monthly Fee</label>
                                        <div class="fs-5">${{ number_format($vendor->monthly_subscription_fee, 2) }}</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="mt-4 pt-3 border-top">
                                @if($subscriptionDetails['needs_subscription'] || $vendor->subscription_status !== 'active')
                                    <a href="{{ route('vendor.subscription.plans') }}" class="btn btn-primary me-2">
                                        <i class="fas fa-credit-card me-1"></i> Subscribe Now
                                    </a>
                                @endif
                                
                                @if($vendor->subscription_status === 'active')
                                    <form action="{{ route('vendor.subscription.cancel') }}" method="POST" class="d-inline">
                                        @csrf
                                        <button type="submit" class="btn btn-outline-danger" 
                                                onclick="return confirm('Are you sure you want to cancel your subscription?')">
                                            <i class="fas fa-times me-1"></i> Cancel Subscription
                                        </button>
                                    </form>
                                @endif
                                
                                <a href="{{ route('vendor.subscription.plans') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-eye me-1"></i> View Plans
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Stats -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Quick Stats</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span class="text-muted">Can Process Orders</span>
                                    @if($subscriptionDetails['can_process_orders'])
                                        <span class="badge bg-success">Yes</span>
                                    @else
                                        <span class="badge bg-danger">No</span>
                                    @endif
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span class="text-muted">Subscription Valid</span>
                                    @if($subscriptionDetails['is_subscription_valid'])
                                        <span class="badge bg-success">Yes</span>
                                    @else
                                        <span class="badge bg-warning">No</span>
                                    @endif
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span class="text-muted">Free Limit</span>
                                    <span class="fw-bold">{{ $vendor->free_order_limit }} orders</span>
                                </div>
                            </div>
                            
                            @if($vendor->subscription_status === 'active')
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span class="text-muted">Next Billing</span>
                                        <span class="fw-bold">
                                            @if($vendor->subscription_expires_at)
                                                {{ $vendor->subscription_expires_at->format('M d') }}
                                            @else
                                                Manual
                                            @endif
                                        </span>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Usage Progress -->
            @if(!$subscriptionDetails['needs_subscription'])
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">Free Order Usage</h6>
                    </div>
                    <div class="card-body">
                        @php
                            $usagePercentage = ($subscriptionDetails['orders_processed'] / $vendor->free_order_limit) * 100;
                            $progressColor = $usagePercentage >= 80 ? 'danger' : ($usagePercentage >= 60 ? 'warning' : 'success');
                        @endphp
                        
                        <div class="d-flex justify-content-between mb-2">
                            <span>{{ $subscriptionDetails['orders_processed'] }} of {{ $vendor->free_order_limit }} orders used</span>
                            <span>{{ number_format($usagePercentage, 1) }}%</span>
                        </div>
                        
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-{{ $progressColor }}" 
                                 style="width: {{ $usagePercentage }}%"></div>
                        </div>
                        
                        @if($usagePercentage >= 80)
                            <div class="alert alert-warning mt-3 mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                You're approaching your free order limit. Consider subscribing to avoid interruption.
                            </div>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Subscription Benefits -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Subscription Benefits</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-infinity text-primary me-2"></i>
                                    Unlimited order processing
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-chart-line text-primary me-2"></i>
                                    Advanced analytics dashboard
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-headset text-primary me-2"></i>
                                    Priority customer support
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-star text-primary me-2"></i>
                                    Featured vendor listing
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-tools text-primary me-2"></i>
                                    Marketing tools access
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-percentage text-primary me-2"></i>
                                    Reduced commission rates
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
