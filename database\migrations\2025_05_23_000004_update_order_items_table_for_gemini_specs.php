<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            // Add price_at_purchase field (rename from price if needed)
            if (Schema::hasColumn('order_items', 'price') && !Schema::hasColumn('order_items', 'price_at_purchase')) {
                $table->renameColumn('price', 'price_at_purchase');
            } elseif (!Schema::hasColumn('order_items', 'price_at_purchase')) {
                $table->decimal('price_at_purchase', 10, 2)->after('quantity');
            }
            
            // Add commission tracking fields
            if (!Schema::hasColumn('order_items', 'commission_rate_snapshot')) {
                $table->decimal('commission_rate_snapshot', 5, 4)
                      ->default(0.027)
                      ->after('price_at_purchase')
                      ->comment('Commission rate at time of purchase (e.g., 0.027 for 2.7%)');
            }
            
            if (!Schema::hasColumn('order_items', 'commission_amount_calculated')) {
                $table->decimal('commission_amount_calculated', 10, 2)
                      ->default(0)
                      ->after('commission_rate_snapshot')
                      ->comment('Calculated commission amount for this item');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->dropColumn([
                'commission_rate_snapshot',
                'commission_amount_calculated'
            ]);
            
            if (Schema::hasColumn('order_items', 'price_at_purchase') && !Schema::hasColumn('order_items', 'price')) {
                $table->renameColumn('price_at_purchase', 'price');
            }
        });
    }
};
