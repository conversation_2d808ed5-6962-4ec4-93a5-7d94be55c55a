<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />

<title>{{ $title ?? config('app.name') }}</title>

<link rel="icon" href="/favicon.ico" sizes="any">
<link rel="icon" href="/favicon.svg" type="image/svg+xml">
<link rel="apple-touch-icon" href="/apple-touch-icon.png">

<!-- Bootstrap 5 CSS CDN -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet"
    integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.bunny.net">
<link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
<link href="https://fonts.bunny.net/css?family=figtree:400,600&display=swap" rel="stylesheet" />

<!-- Font Awesome Icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
    integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
    crossorigin="anonymous" referrerpolicy="no-referrer" />

<!-- Alpine.js CDN -->
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js"></script>

<!-- Bootstrap 5 JS Bundle with Popper -->
<script defer src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
    integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous">
</script>

<!-- Custom Styles for Black and White Theme -->
<style>
    :root {
        --primary-color: #000;
        --secondary-color: #fff;
        --text-color: #000;
        --bg-color: #fff;
        --gray-100: #f8f9fa;
        --gray-200: #e9ecef;
        --gray-300: #dee2e6;
        --gray-400: #ced4da;
        --gray-500: #adb5bd;
        --gray-600: #6c757d;
        --gray-700: #495057;
        --gray-800: #343a40;
        --gray-900: #212529;
    }

    /* Hero Slider Styles */
    .hero-slider {
        position: relative;
        overflow: hidden;
        background-size: cover;
        background-position: center;
    }
    
    .hero-slider .hero-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.2);
        z-index: 1;
    }
    
    .slider-prev,
    .slider-next {
        transition: all 0.3s ease;
        opacity: 0.8;
    }
    
    .slider-prev:hover,
    .slider-next:hover {
        opacity: 1;
        transform: translateY(-50%) scale(1.1);
    }
    
    .slider-dot {
        transition: all 0.3s ease;
        border: none;
        padding: 0;
    }
    
    .slider-dot.active {
        width: 20px !important;
        border-radius: 6px !important;
    }
    
    @media (max-width: 767.98px) {
        .hero-slider {
            min-height: 300px !important;
        }
        
        .slider-prev,
        .slider-next {
            width: 36px !important;
            height: 36px !important;
            font-size: 0.8rem;
        }
    }
    
    body {
        font-family: 'Figtree', 'Instrument Sans', sans-serif;
        color: var(--text-color);
        background-color: var(--bg-color);
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .hero-slider {
        position: relative;
        width: 100%;
        height: 500px;
        overflow: hidden;
        background-size: cover;
        background-position: center;
        transition: opacity 0.5s ease-in-out;
        opacity: 1;
    }

    .hero-slider.fade-out {
        opacity: 0;
    }

    .hero-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to right, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6));
    }

    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: var(--secondary-color);
    }

    .btn-primary:hover {
        background-color: var(--gray-800);
        border-color: var(--gray-800);
    }

    .btn-outline-primary {
        border-color: var(--secondary-color);
        color: var(--secondary-color);
    }

    .btn-outline-primary:hover {
        background-color: var(--secondary-color);
        color: var(--primary-color);
    }

    .card {
        transition: all 0.3s ease;
        border: 1px solid var(--gray-300);
    }

    .card:hover {
        transform: translateY(-5px);
        border-color: var(--primary-color);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .border-2 {
        border-width: 2px !important;
    }

    .hover-border-dark:hover {
        border-color: #000 !important;
    }

    .h-40 {
        height: 160px;
    }

    .transition-transform {
        transition: transform 0.3s ease;
    }

    a:hover .transition-transform {
        transform: translateX(5px);
    }

    .nav-link {
        color: var(--gray-700);
        font-weight: 500;
        position: relative;
    }

    .nav-link:hover {
        color: var(--primary-color);
    }

    .nav-link::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 0;
        height: 2px;
        background-color: var(--primary-color);
        transition: width 0.3s ease;
    }

    .nav-link:hover::after {
        width: 100%;
    }

    .badge-new {
        background-color: var(--primary-color);
        color: var(--secondary-color);
    }

    .text-xs {
        font-size: 0.75rem;
    }

    .top-2 {
        top: 0.5rem;
    }

    .start-2 {
        left: 0.5rem;
    }

    .footer {
        background-color: var(--primary-color);
        color: var(--secondary-color);
    }

    .footer a {
        color: var(--gray-400);
        transition: color 0.2s ease;
    }

    .footer a:hover {
        color: var(--secondary-color);
        text-decoration: none;
    }

    @media (max-width: 768px) {
        .hero-slider {
            height: 400px;
        }
    }
</style>

@fluxAppearance
