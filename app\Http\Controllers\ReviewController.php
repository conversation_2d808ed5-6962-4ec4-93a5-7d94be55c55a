<?php

namespace App\Http\Controllers;

use App\Http\Requests\ReviewStoreRequest;
use App\Models\Product;
use App\Models\Review;
use Illuminate\Http\Request;

class ReviewController extends Controller
{
    /**
     * Store a newly created review in storage.
     */
    public function store(ReviewStoreRequest $request, Product $product)
    {
        // Check if user has already reviewed this product
        if ($product->hasUserReviewed(auth()->id())) {
            return back()->with('error', 'You have already reviewed this product.');
        }

        // Create the review
        $review = Review::create([
            'user_id' => auth()->id(),
            'product_id' => $product->id,
            'vendor_id' => $product->vendor_id,
            'rating' => $request->rating,
            'comment' => $request->comment,
            'is_approved' => false, // Reviews need admin approval
        ]);

        return back()->with('success', 'Thank you for your review! It will be published after approval.');
    }

    /**
     * Get reviews for a product (AJAX endpoint)
     */
    public function getProductReviews(Product $product)
    {
        $reviews = $product->approvedReviews()
            ->with(['user', 'vendor'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json([
            'reviews' => $reviews->items(),
            'pagination' => [
                'current_page' => $reviews->currentPage(),
                'last_page' => $reviews->lastPage(),
                'total' => $reviews->total(),
            ],
            'average_rating' => $product->getAverageRating(),
            'review_count' => $product->getReviewCount(),
        ]);
    }
}
