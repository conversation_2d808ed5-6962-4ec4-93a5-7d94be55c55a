<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Vendor;
use App\Models\User;

class VendorController extends Controller
{
    public function index()
    {
        $vendors = Vendor::with('user')->paginate(15);
        return view('admin.vendors.index', compact('vendors'));
    }
    public function show(Vendor $vendor)
    {
        return view('admin.vendors.show', compact('vendor'));
    }
    public function edit(Vendor $vendor)
    {
        return view('admin.vendors.edit', compact('vendor'));
    }
    public function update(Request $request, Vendor $vendor)
    {
        $data = $request->validate([
            'shop_name' => 'required',
            'slug' => 'required|unique:vendors,slug,'.$vendor->id,
            'description' => 'nullable',
            'address' => 'nullable',
            'city' => 'nullable',
            'state' => 'nullable',
            'country' => 'nullable',
            'logo' => 'nullable',
            'is_approved' => 'boolean',
            'is_featured' => 'boolean',
        ]);
        $vendor->update($data);
        return redirect()->route('admin.vendors.index')->with('success', 'Vendor updated successfully.');
    }
    public function destroy(Vendor $vendor)
    {
        $vendor->delete();
        return redirect()->route('admin.vendors.index')->with('success', 'Vendor deleted successfully.');
    }
    public function approve($id)
    {
        $vendor = Vendor::findOrFail($id);
        $vendor->is_approved = true;
        $vendor->save();
        return redirect()->route('admin.vendors.index')->with('success', 'Vendor approved successfully.');
    }
    
    public function toggleFeatured($id)
    {
        $vendor = Vendor::findOrFail($id);
        $vendor->is_featured = !$vendor->is_featured;
        $vendor->save();
        
        $status = $vendor->is_featured ? 'featured' : 'unfeatured';
        return redirect()->route('admin.vendors.index')->with('success', "Vendor {$status} successfully.");
    }
    public function reject($id)
    {
        $vendor = Vendor::findOrFail($id);
        $vendor->is_approved = false;
        $vendor->save();
        return redirect()->route('admin.vendors.index')->with('success', 'Vendor rejected.');
    }
}
