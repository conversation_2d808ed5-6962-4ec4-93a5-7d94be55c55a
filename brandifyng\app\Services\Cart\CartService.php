<?php

namespace App\Services\Cart;

use App\Models\Product;
use Illuminate\Support\Collection;
use Illuminate\Session\SessionManager;

class CartService
{
    protected $session;
    protected $cart;

    /**
     * CartService constructor.
     */
    public function __construct()
    {
        $this->session = app(SessionManager::class);
        $this->cart = $this->session->get('cart', new Collection());
    }

    /**
     * Add an item to the cart.
     *
     * @param Product $product
     * @param int $quantity
     * @return void
     */
    public function add(Product $product, int $quantity = 1): void
    {
        $cartItem = $this->cart->get($product->id);

        if (!$cartItem) {
            $this->cart->put($product->id, [
                'id' => $product->id,
                'name' => $product->name,
                'price' => $product->getCurrentPrice(),
                'quantity' => $quantity,
                'image_url' => $product->image_url,
                'vendor_id' => $product->vendor_id,
            ]);
        } else {
            $cartItem['quantity'] += $quantity;
            $this->cart->put($product->id, $cartItem);
        }

        $this->session->put('cart', $this->cart);
    }

    /**
     * Update the quantity of an item in the cart.
     *
     * @param int $id
     * @param int $quantity
     * @return void
     */
    public function update(int $id, int $quantity): void
    {
        $cartItem = $this->cart->get($id);

        if ($cartItem) {
            $cartItem['quantity'] = $quantity;
            $this->cart->put($id, $cartItem);
            $this->session->put('cart', $this->cart);
        }
    }

    /**
     * Remove an item from the cart.
     *
     * @param int $id
     * @return void
     */
    public function remove(int $id): void
    {
        $this->cart->forget($id);
        $this->session->put('cart', $this->cart);
    }

    /**
     * Clear the cart.
     *
     * @return void
     */
    public function clear(): void
    {
        $this->cart = new Collection();
        $this->session->put('cart', $this->cart);
    }

    /**
     * Get the cart contents.
     *
     * @return Collection
     */
    public function content(): Collection
    {
        return $this->cart;
    }

    /**
     * Get the number of items in the cart.
     *
     * @return int
     */
    public function count(): int
    {
        return $this->cart->sum('quantity');
    }

    /**
     * Get the total price of the items in the cart.
     *
     * @return float
     */
    public function total(): float
    {
        return $this->cart->sum(function ($item) {
            return $item['price'] * $item['quantity'];
        });
    }

    /**
     * Get the subtotal price of the items in the cart (without tax).
     *
     * @return float
     */
    public function subtotal(): float
    {
        return $this->total();
    }

    /**
     * Get the tax amount for the items in the cart.
     *
     * @param float $taxRate
     * @return float
     */
    public function tax(float $taxRate = 0.08): float
    {
        return $this->subtotal() * $taxRate;
    }
}
