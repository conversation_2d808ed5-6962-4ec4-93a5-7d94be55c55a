<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Commission;
use App\Models\Vendor;

class CommissionController extends Controller
{
    public function index()
    {
        $commissions = Commission::with('vendor')->paginate(15);
        return view('admin.commissions.index', compact('commissions'));
    }
    public function update(Request $request, Commission $commission)
    {
        $data = $request->validate([
            'status' => 'required|in:pending,paid',
        ]);
        $commission->status = $data['status'];
        $commission->save();
        return redirect()->route('admin.commissions.index')->with('success', 'Commission status updated.');
    }
}
