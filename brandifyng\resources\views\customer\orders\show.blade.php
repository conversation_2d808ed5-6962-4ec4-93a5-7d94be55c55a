@extends('layouts.app')

@section('content')
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-decoration-none text-dark">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}" class="text-decoration-none text-dark">My Account</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('orders.index') }}" class="text-decoration-none text-dark">Orders</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Order #{{ $order->order_number }}</li>
                </ol>
            </nav>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h1 class="fw-bold mb-0">Order #{{ $order->order_number }}</h1>
            <a href="{{ route('orders.index') }}" class="btn btn-outline-dark">
                <i class="fas fa-arrow-left me-2"></i> Back to Orders
            </a>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white py-3">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="fw-bold mb-0">Order Details</h5>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <span class="text-muted me-3">Order Date: {{ $order->created_at->format('M d, Y') }}</span>
                            @if($order->status === 'completed')
                                <span class="badge bg-success">Completed</span>
                            @elseif($order->status === 'processing')
                                <span class="badge bg-warning text-dark">Processing</span>
                            @elseif($order->status === 'shipped')
                                <span class="badge bg-info">Shipped</span>
                            @elseif($order->status === 'cancelled')
                                <span class="badge bg-danger">Cancelled</span>
                            @else
                                <span class="badge bg-secondary">{{ ucfirst($order->status) }}</span>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6 mb-4 mb-md-0">
                            <h6 class="fw-bold mb-3">Shipping Address</h6>
                            <p class="mb-1">{{ $order->shipping_name }}</p>
                            <p class="mb-1">{{ $order->shipping_address }}</p>
                            <p class="mb-1">{{ $order->shipping_city }}, {{ $order->shipping_state }} {{ $order->shipping_postal_code }}</p>
                            <p class="mb-1">{{ $order->shipping_country }}</p>
                            <p class="mb-0">{{ $order->shipping_phone }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">Order Information</h6>
                            <p class="mb-1"><strong>Payment Method:</strong> {{ ucfirst($order->payment_method) }}</p>
                            <p class="mb-1"><strong>Payment Status:</strong> 
                                @if($order->payment_status === 'paid')
                                    <span class="badge bg-success">Paid</span>
                                @elseif($order->payment_status === 'pending')
                                    <span class="badge bg-warning text-dark">Pending</span>
                                @else
                                    <span class="badge bg-danger">Failed</span>
                                @endif
                            </p>
                            <p class="mb-1"><strong>Shipping Method:</strong> {{ $order->shipping_method ?? 'Standard Shipping' }}</p>
                            @if($order->shipped_at)
                                <p class="mb-1"><strong>Shipped Date:</strong> {{ $order->shipped_at->format('M d, Y') }}</p>
                            @endif
                            @if($order->delivered_at)
                                <p class="mb-0"><strong>Delivered Date:</strong> {{ $order->delivered_at->format('M d, Y') }}</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="fw-bold mb-0">Order Items</h5>
                    <div class="order-actions">
                        @if($order->isPending() || $order->isProcessing())
                            <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#cancelOrderModal">
                                Cancel Order
                            </button>
                        @endif
                        
                        @if($order->isCompleted())
                            <button type="button" class="btn btn-sm btn-outline-warning" data-bs-toggle="modal" data-bs-target="#returnOrderModal">
                                Request Return
                            </button>
                        @endif
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th scope="col" class="border-0">Product</th>
                                    <th scope="col" class="border-0">Price</th>
                                    <th scope="col" class="border-0">Quantity</th>
                                    <th scope="col" class="border-0">Subtotal</th>
                                    <th scope="col" class="border-0">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($order->items as $item)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{{ $item->product->image_url ?? 'https://via.placeholder.com/80x80?text='.$item->product_name }}" alt="{{ $item->product_name }}" class="img-fluid rounded me-3" style="width: 80px; height: 80px; object-fit: cover;">
                                            <div>
                                                <h6 class="fw-bold mb-1">{{ $item->product_name }}</h6>
                                                <p class="text-muted mb-0">Vendor: {{ $item->vendor->shop_name ?? 'N/A' }}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>${{ number_format($item->unit_price, 2) }}</td>
                                    <td>{{ $item->quantity }}</td>
                                    <td>${{ number_format($item->subtotal, 2) }}</td>
                                    <td>
                                        @if($item->status === 'completed')
                                            <span class="badge bg-success">Completed</span>
                                        @elseif($item->status === 'processing')
                                            <span class="badge bg-warning text-dark">Processing</span>
                                        @elseif($item->status === 'shipped')
                                            <span class="badge bg-info">Shipped</span>
                                        @elseif($item->status === 'cancelled')
                                            <span class="badge bg-danger">Cancelled</span>
                                        @else
                                            <span class="badge bg-secondary">{{ ucfirst($item->status) }}</span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white py-3">
                    <h5 class="fw-bold mb-0">Order Notes</h5>
                </div>
                <div class="card-body">
                    @if($order->notes)
                        <p class="mb-0">{{ $order->notes }}</p>
                    @else
                        <p class="text-muted mb-0">No notes for this order.</p>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white py-3">
                    <h5 class="fw-bold mb-0">Order Summary</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal</span>
                        <span>${{ number_format($order->total_amount - $order->tax_amount - $order->shipping_amount + $order->discount_amount, 2) }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Shipping</span>
                        <span>${{ number_format($order->shipping_amount, 2) }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Tax</span>
                        <span>${{ number_format($order->tax_amount, 2) }}</span>
                    </div>
                    @if($order->discount_amount > 0)
                    <div class="d-flex justify-content-between mb-2">
                        <span>Discount</span>
                        <span>-${{ number_format($order->discount_amount, 2) }}</span>
                    </div>
                    @endif
                    <hr>
                    <div class="d-flex justify-content-between">
                        <span class="fw-bold">Total</span>
                        <span class="fw-bold fs-5">${{ number_format($order->total_amount, 2) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white py-3">
                    <h5 class="fw-bold mb-0">Order Timeline</h5>
                </div>
                <div class="card-body p-4">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-item-marker">
                                <div class="timeline-item-marker-indicator bg-success"></div>
                            </div>
                            <div class="timeline-item-content">
                                <span class="fw-bold">Order Placed</span>
                                <p class="mb-0 text-muted">{{ $order->created_at->format('M d, Y h:i A') }}</p>
                            </div>
                        </div>
                        
                        @if($order->payment_status === 'paid')
                        <div class="timeline-item">
                            <div class="timeline-item-marker">
                                <div class="timeline-item-marker-indicator bg-success"></div>
                            </div>
                            <div class="timeline-item-content">
                                <span class="fw-bold">Payment Confirmed</span>
                                <p class="mb-0 text-muted">{{ $order->paid_at ? $order->paid_at->format('M d, Y h:i A') : $order->created_at->addMinutes(5)->format('M d, Y h:i A') }}</p>
                            </div>
                        </div>
                        @endif
                        
                        @if($order->status === 'processing' || $order->status === 'shipped' || $order->status === 'completed')
                        <div class="timeline-item">
                            <div class="timeline-item-marker">
                                <div class="timeline-item-marker-indicator bg-success"></div>
                            </div>
                            <div class="timeline-item-content">
                                <span class="fw-bold">Order Processing</span>
                                <p class="mb-0 text-muted">{{ $order->created_at->addHours(2)->format('M d, Y h:i A') }}</p>
                            </div>
                        </div>
                        @endif
                        
                        @if($order->status === 'shipped' || $order->status === 'completed')
                        <div class="timeline-item">
                            <div class="timeline-item-marker">
                                <div class="timeline-item-marker-indicator bg-success"></div>
                            </div>
                            <div class="timeline-item-content">
                                <span class="fw-bold">Order Shipped</span>
                                <p class="mb-0 text-muted">{{ $order->shipped_at ? $order->shipped_at->format('M d, Y h:i A') : $order->created_at->addDays(1)->format('M d, Y h:i A') }}</p>
                            </div>
                        </div>
                        @endif
                        
                        @if($order->status === 'completed')
                        <div class="timeline-item">
                            <div class="timeline-item-marker">
                                <div class="timeline-item-marker-indicator bg-success"></div>
                            </div>
                            <div class="timeline-item-content">
                                <span class="fw-bold">Order Delivered</span>
                                <p class="mb-0 text-muted">{{ $order->delivered_at ? $order->delivered_at->format('M d, Y h:i A') : $order->created_at->addDays(3)->format('M d, Y h:i A') }}</p>
                            </div>
                        </div>
                        @endif
                        
                        @if($order->status === 'cancelled')
                        <div class="timeline-item">
                            <div class="timeline-item-marker">
                                <div class="timeline-item-marker-indicator bg-danger"></div>
                            </div>
                            <div class="timeline-item-content">
                                <span class="fw-bold">Order Cancelled</span>
                                <p class="mb-0 text-muted">{{ $order->cancelled_at ? $order->cancelled_at->format('M d, Y h:i A') : $order->updated_at->format('M d, Y h:i A') }}</p>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cancel Order Modal -->
<div class="modal fade" id="cancelOrderModal" tabindex="-1" aria-labelledby="cancelOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cancelOrderModalLabel">Cancel Order</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('orders.cancel', $order) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <p>Are you sure you want to cancel this order? This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-danger">Yes, Cancel Order</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Return Order Modal -->
<div class="modal fade" id="returnOrderModal" tabindex="-1" aria-labelledby="returnOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="returnOrderModalLabel">Request Return</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('orders.return', $order) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="return_reason" class="form-label">Return Reason</label>
                        <textarea class="form-control" id="return_reason" name="return_reason" rows="4" required placeholder="Please explain the reason for returning this order"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-warning">Submit Return Request</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    .timeline {
        position: relative;
        padding-left: 2rem;
    }
    
    .timeline-item {
        position: relative;
        padding-bottom: 2rem;
    }
    
    .timeline-item:last-child {
        padding-bottom: 0;
    }
    
    .timeline-item:before {
        content: '';
        position: absolute;
        left: -1.5rem;
        top: 0;
        height: 100%;
        width: 2px;
        background-color: #e9ecef;
    }
    
    .timeline-item-marker {
        position: absolute;
        left: -2rem;
        top: 0;
    }
    
    .timeline-item-marker-indicator {
        width: 1rem;
        height: 1rem;
        border-radius: 100%;
        background-color: #fff;
        border: 2px solid #e9ecef;
    }
    
    .timeline-item-content {
        padding-top: 0;
    }
</style>
@endsection
