<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class SubscriptionController extends Controller
{
    /**
     * Show subscription index page (redirects to status)
     */
    public function index()
    {
        return redirect()->route('vendor.subscription.status');
    }

    /**
     * Show subscription plans page
     */
    public function plans()
    {
        $vendor = auth()->user()->vendor;
        $subscriptionDetails = $vendor->getSubscriptionStatusDetails();

        $plans = [
            [
                'name' => 'Basic Plan',
                'price' => 50,
                'duration' => 1, // months
                'features' => [
                    'Unlimited order processing',
                    'Basic analytics dashboard',
                    'Email support',
                    'Standard commission rates'
                ],
                'recommended' => true
            ],
            [
                'name' => 'Pro Plan',
                'price' => 120,
                'duration' => 3, // months
                'features' => [
                    'Unlimited order processing',
                    'Advanced analytics dashboard',
                    'Priority email support',
                    'Reduced commission rates',
                    'Featured vendor listing'
                ],
                'recommended' => false
            ],
            [
                'name' => 'Annual Plan',
                'price' => 400,
                'duration' => 12, // months
                'features' => [
                    'Unlimited order processing',
                    'Premium analytics dashboard',
                    'Phone & email support',
                    'Lowest commission rates',
                    'Featured vendor listing',
                    'Marketing tools access'
                ],
                'recommended' => false
            ]
        ];

        return view('vendor.subscription.plans', compact('vendor', 'subscriptionDetails', 'plans'));
    }

    /**
     * Show subscription status page
     */
    public function status()
    {
        $vendor = auth()->user()->vendor;
        $subscriptionDetails = $vendor->getSubscriptionStatusDetails();

        return view('vendor.subscription.status', compact('vendor', 'subscriptionDetails'));
    }

    /**
     * Process subscription purchase with Paystack
     */
    public function subscribe(Request $request)
    {
        $request->validate([
            'plan' => 'required|in:basic,pro,annual',
            'payment_method' => 'required|string'
        ]);

        $vendor = auth()->user()->vendor;

        // Define plan details (prices in Naira)
        $planDetails = [
            'basic' => ['duration' => 30, 'price' => 5000], // ₦5,000 monthly
            'pro' => ['duration' => 90, 'price' => 12000],  // ₦12,000 quarterly
            'annual' => ['duration' => 365, 'price' => 40000] // ₦40,000 annually
        ];

        $selectedPlan = $planDetails[$request->plan];

        try {
            // Initialize Paystack payment
            $paymentData = $this->initializePaystackPayment(
                $vendor,
                $selectedPlan['price'],
                $request->plan
            );

            if ($paymentData['status']) {
                // Store payment reference in session
                session([
                    'subscription_payment_reference' => $paymentData['data']['reference'],
                    'subscription_plan' => $request->plan,
                    'subscription_amount' => $selectedPlan['price']
                ]);

                // Redirect to Paystack checkout
                return redirect($paymentData['data']['authorization_url']);
            } else {
                throw new \Exception('Failed to initialize payment: ' . $paymentData['message']);
            }

        } catch (\Exception $e) {
            return redirect()->back()
                           ->with('error', 'Payment initialization failed: ' . $e->getMessage());
        }
    }

    /**
     * Cancel subscription
     */
    public function cancel()
    {
        $vendor = auth()->user()->vendor;

        $vendor->update([
            'subscription_status' => 'cancelled'
        ]);

        return redirect()->route('vendor.subscription.status')
                       ->with('info', 'Subscription cancelled. You can continue processing orders until your current subscription expires.');
    }

    /**
     * Initialize Paystack payment for subscription
     */
    private function initializePaystackPayment($vendor, $amount, $plan)
    {
        $url = "https://api.paystack.co/transaction/initialize";

        $fields = [
            'email' => $vendor->user->email,
            'amount' => $amount * 100, // Convert to kobo
            'reference' => 'SUB_' . uniqid(),
            'callback_url' => route('vendor.subscription.callback'),
            'metadata' => [
                'vendor_id' => $vendor->id,
                'plan' => $plan,
                'type' => 'subscription'
            ]
        ];

        $fields_string = http_build_query($fields);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $fields_string);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: Bearer " . config('services.paystack.secret_key'),
            "Cache-Control: no-cache",
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result, true);
    }

    /**
     * Handle Paystack callback for subscription
     */
    public function callback(Request $request)
    {
        $reference = $request->query('reference');

        if (!$reference) {
            return redirect()->route('vendor.subscription.index')
                           ->with('error', 'Payment reference not found.');
        }

        // Verify payment with Paystack
        $paymentData = $this->verifyPaystackPayment($reference);

        if ($paymentData['status'] && $paymentData['data']['status'] === 'success') {
            $vendor = auth()->user()->vendor;
            $plan = session('subscription_plan');

            // Define plan details
            $planDetails = [
                'basic' => ['duration' => 30, 'price' => 5000],
                'pro' => ['duration' => 90, 'price' => 12000],
                'annual' => ['duration' => 365, 'price' => 40000]
            ];

            $selectedPlan = $planDetails[$plan];

            // Activate subscription
            $vendor->activateSubscription($selectedPlan['duration']);

            // Clear session data
            session()->forget(['subscription_payment_reference', 'subscription_plan', 'subscription_amount']);

            return redirect()->route('vendor.subscription.index')
                           ->with('success', 'Subscription activated successfully! You can now process unlimited orders.');
        } else {
            return redirect()->route('vendor.subscription.index')
                           ->with('error', 'Payment verification failed. Please contact support.');
        }
    }

    /**
     * Verify Paystack payment
     */
    private function verifyPaystackPayment($reference)
    {
        $url = "https://api.paystack.co/transaction/verify/" . $reference;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: Bearer " . config('services.paystack.secret_key'),
            "Cache-Control: no-cache",
        ]);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result, true);
    }

    /**
     * Get subscription modal data (for AJAX requests)
     */
    public function modal()
    {
        $vendor = auth()->user()->vendor;
        $subscriptionDetails = $vendor->getSubscriptionStatusDetails();

        return response()->json([
            'vendor' => $vendor,
            'subscription_details' => $subscriptionDetails,
            'plans_url' => route('vendor.subscription.plans')
        ]);
    }
}
