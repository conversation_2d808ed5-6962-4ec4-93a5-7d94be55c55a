<?php

namespace App\Providers;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Blade::directive('currency', function ($expression) {
            $currencySymbol = config('brandify.currency.symbol', '₦');
            return "<?php echo '{$currencySymbol}' . number_format($expression, 2); ?>";
        });
    }
}
