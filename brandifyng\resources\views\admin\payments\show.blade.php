@extends('layouts.admin')
@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="fw-bold">Payment #{{ $payment->id }}</h2>
        <a href="{{ route('admin.payments.index') }}" class="btn btn-outline-dark">Back</a>
    </div>
    <div class="card">
        <div class="card-body">
            <div class="mb-2"><strong>Reference:</strong> {{ $payment->reference }}</div>
            <div class="mb-2"><strong>User:</strong> {{ $payment->user->name ?? '' }} ({{ $payment->user->email ?? '' }})</div>
            <div class="mb-2"><strong>Amount:</strong> ${{ number_format($payment->amount, 2) }}</div>
            <div class="mb-2"><strong>Status:</strong> <span class="badge bg-dark">{{ ucfirst($payment->status) }}</span></div>
            <div class="mb-2"><strong>Date:</strong> {{ $payment->created_at->format('M d, Y') }}</div>
        </div>
    </div>
</div>
@endsection
