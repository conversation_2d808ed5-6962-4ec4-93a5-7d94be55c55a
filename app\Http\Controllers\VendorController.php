<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Vendor;
use App\Models\Role;
use App\Services\DeliveryZoneService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class VendorController extends Controller
{
    /**
     * Display the vendor registration form
     */
    public function register()
    {
        $deliveryZoneService = new DeliveryZoneService();
        $nigerianStates = $deliveryZoneService->getAllNigerianStates();
        $platformDeliveryStates = $deliveryZoneService->getPlatformDeliveryStates();

        return view('vendor.register', compact('nigerianStates', 'platformDeliveryStates'));
    }
    
    /**
     * Process the vendor registration
     */
    public function store(Request $request)
    {
        // Validate request
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'contact_phone' => 'required|string|max:20',
            'business_name' => 'required|string|max:255|unique:vendors,business_name',
            'address_line1' => 'required|string|max:500',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'contact_email_business' => 'nullable|email|max:255',
            'business_description' => 'required|string|max:1000',
            'logo' => 'nullable|image|mimes:jpg,jpeg,png,gif|max:2048',
            'id_document' => 'required|file|mimes:jpg,jpeg,png,pdf|max:5120',
            'business_document' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:5120',
            'terms' => 'required|accepted',
        ]);

        // Initialize delivery zone service
        $deliveryZoneService = new DeliveryZoneService();

        // Validate Nigerian phone number
        if (!$deliveryZoneService->validateNigerianPhone($request->contact_phone)) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['contact_phone' => 'Please enter a valid Nigerian phone number.']);
        }
        
        // Get the vendor role ID
        $vendorRole = Role::where('name', 'vendor')->first();
        if (!$vendorRole) {
            return redirect()->back()->with('error', 'Vendor role not found in the system. Please contact administrator.');
        }

        // Determine delivery zone type based on state
        $deliveryZoneType = $deliveryZoneService->determineDeliveryZoneType($request->state);
        $subscriptionFee = $deliveryZoneService->getSubscriptionFee($deliveryZoneType);
        
        // Create a new user
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role_id' => $vendorRole->id,
        ]);
        
        // Process uploaded files
        $logoPath = null;
        $idDocPath = null;
        $businessDocPath = null;
        
        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('public/vendor/logos');
            $logoPath = Storage::url($logoPath);
        }
        
        if ($request->hasFile('id_document')) {
            $idDocPath = $request->file('id_document')->store('private/vendor/documents');
        }
        
        if ($request->hasFile('business_document')) {
            $businessDocPath = $request->file('business_document')->store('private/vendor/documents');
        }
        
        // Create a vendor record
        $vendor = Vendor::create([
            'user_id' => $user->id,
            'business_name' => $request->business_name,
            'shop_name' => $request->business_name, // For backward compatibility
            'slug' => Str::slug($request->business_name),
            'address_line1' => $request->address_line1,
            'address' => $request->address_line1, // For backward compatibility
            'city' => $request->city,
            'state' => $request->state,
            'country' => 'Nigeria',
            'contact_phone' => $deliveryZoneService->formatNigerianPhone($request->contact_phone),
            'contact_email_business' => $request->contact_email_business,
            'delivery_zone_type' => $deliveryZoneType,
            'subscription_status' => 'pending_payment',
            'description' => $request->business_description,
            'logo' => $logoPath,
            'approved' => false,
            'is_approved' => false, // For backward compatibility
        ]);
        
        // Log the user in
        auth()->login($user);

        // Prepare success message with delivery zone information
        $deliveryPolicy = $deliveryZoneService->getDeliveryPolicy($deliveryZoneType);
        $successMessage = "Your vendor account has been created successfully! ";
        $successMessage .= "You are in the {$deliveryPolicy['type']} zone. ";
        $successMessage .= "Subscription fee: ₦" . number_format($subscriptionFee) . ". ";
        $successMessage .= "Your application is pending approval from our team.";

        // Redirect to vendor dashboard with onboarding message
        return redirect()->route('vendor.dashboard')->with('success', $successMessage);
    }
    
    /**
     * Display the vendor onboarding page
     */
    public function onboarding()
    {
        if (!auth()->user()->hasRole('vendor')) {
            return redirect()->route('vendor.register');
        }
        
        $vendor = auth()->user()->vendor;
        
        if (!$vendor) {
            return redirect()->route('vendor.register');
        }
        
        return view('vendor.onboarding', compact('vendor'));
    }
    
    /**
     * Complete the vendor profile
     */
    public function completeProfile(Request $request)
    {
        // Validate request
        $request->validate([
            'bank_name' => 'required|string|max:255',
            'bank_account_name' => 'required|string|max:255',
            'bank_account_number' => 'required|string|max:20',
            'tax_id' => 'nullable|string|max:50',
            'paypal_email' => 'nullable|email|max:255',
            'commission_agreement' => 'required|accepted',
        ]);
        
        $vendor = auth()->user()->vendor;
        
        // Update vendor with banking details
        $vendor->update([
            'bank_name' => $request->bank_name,
            'bank_account_name' => $request->bank_account_name,
            'bank_account_number' => $request->bank_account_number,
            'tax_id' => $request->tax_id,
            'paypal_email' => $request->paypal_email,
            'has_completed_onboarding' => true,
        ]);
        
        return redirect()->route('vendor.dashboard')->with('success', 'Your vendor profile has been completed successfully!');
    }
    
    /**
     * Display the vendor storefront
     */
    public function show($slug)
    {
        $vendor = Vendor::where('slug', $slug)
            ->where('is_approved', true)
            ->where('status', 'active')
            ->firstOrFail();
            
        $products = $vendor->products()
            ->where('is_active', true)
            ->paginate(12);
            
        return view('vendor.show', compact('vendor', 'products'));
    }
}
