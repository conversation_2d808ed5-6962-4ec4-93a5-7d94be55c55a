<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Category;
use App\Models\Product;
use Illuminate\Support\Str;

class BasicTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create categories first
        $categories = [
            ['name' => 'Clothing', 'slug' => 'clothing', 'is_active' => true],
            ['name' => 'Electronics', 'slug' => 'electronics', 'is_active' => true],
            ['name' => 'Home & Garden', 'slug' => 'home-garden', 'is_active' => true],
            ['name' => 'Sports', 'slug' => 'sports', 'is_active' => true],
        ];

        foreach ($categories as $categoryData) {
            Category::firstOrCreate(
                ['slug' => $categoryData['slug']],
                $categoryData
            );
        }

        // Create test vendor user
        $vendorUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Vendor',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'role' => 'vendor'
            ]
        );

        // Create test vendor
        $vendor = Vendor::firstOrCreate(
            ['user_id' => $vendorUser->id],
            [
                'shop_name' => 'Test Fashion Store',
                'slug' => 'test-fashion-store',
                'description' => 'A test fashion store for testing purposes',
                'brand_description' => 'Premium fashion brand focused on quality and style',
                'address' => '123 Test Street',
                'city' => 'Lagos',
                'state' => 'Lagos',
                'country' => 'Nigeria',
                'is_approved' => true,
                'is_featured' => true,
                'orders_processed' => 5,
                'free_order_limit' => 10,
                'subscription_status' => 'inactive',
            ]
        );

        // Create admin user
        $adminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Admin',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'role' => 'admin'
            ]
        );

        // Create customer user
        $customerUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Customer',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'role' => 'customer'
            ]
        );

        // Create test products
        $clothingCategory = Category::where('slug', 'clothing')->first();
        
        $products = [
            [
                'name' => 'Premium Cotton T-Shirt',
                'description' => 'High-quality cotton t-shirt perfect for everyday wear',
                'price' => 2500.00,
                'stock_quantity' => 50,
            ],
            [
                'name' => 'Designer Jeans',
                'description' => 'Stylish designer jeans with perfect fit',
                'price' => 8500.00,
                'stock_quantity' => 30,
            ],
            [
                'name' => 'Casual Sneakers',
                'description' => 'Comfortable casual sneakers for daily use',
                'price' => 12000.00,
                'stock_quantity' => 25,
            ],
            [
                'name' => 'Summer Dress',
                'description' => 'Beautiful summer dress for special occasions',
                'price' => 6500.00,
                'stock_quantity' => 20,
            ],
        ];

        foreach ($products as $productData) {
            Product::firstOrCreate(
                [
                    'vendor_id' => $vendor->id,
                    'name' => $productData['name']
                ],
                [
                    'vendor_id' => $vendor->id,
                    'category_id' => $clothingCategory->id,
                    'slug' => Str::slug($productData['name']) . '-' . uniqid(),
                    'description' => $productData['description'],
                    'price' => $productData['price'],
                    'stock_quantity' => $productData['stock_quantity'],
                    'is_active' => true,
                    'is_featured' => rand(0, 1) == 1,
                ]
            );
        }

        $this->command->info('Basic test data seeded successfully!');
        $this->command->info('Test accounts created:');
        $this->command->info('- Admin: <EMAIL> / password');
        $this->command->info('- Vendor: <EMAIL> / password');
        $this->command->info('- Customer: <EMAIL> / password');
    }
}
