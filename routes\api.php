<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\VendorOrdersController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Vendor Dashboard API Routes
Route::middleware(['auth', 'role:vendor'])->prefix('vendor')->group(function () {
    // Nigeria Map Widget data endpoint
    Route::get('/orders/by-state', [VendorOrdersController::class, 'getOrdersByState']);
    
    // Dashboard Analytics Charts data endpoint
    Route::get('/analytics', [\App\Http\Controllers\Api\VendorAnalyticsController::class, 'getAnalytics']);
});
