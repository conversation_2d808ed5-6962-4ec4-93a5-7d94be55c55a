<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\ProductVariant;

class Size extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'code'];

    /**
     * Get the product variants associated with the size.
     */
    public function productVariants()
    {
        return $this->hasMany(ProductVariant::class);
    }
}
