/**
 * Cart AJAX Functionality - Completely Redesigned
 * Handles add to cart, update quantity, and remove item operations without page reload
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeCartFunctionality();
});

function initializeCartFunctionality() {
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    // Add to Cart forms
    const addToCartForms = document.querySelectorAll('form[action*="cart/add"], form.ajax-add-to-cart-form');
    addToCartForms.forEach(form => {
        const newForm = form.cloneNode(true);
        form.parentNode.replaceChild(newForm, form);
        newForm.addEventListener('submit', (e) => handleCartAction(e, 'add'));
    });

    // Cart Update forms (for quantity changes)
    const cartUpdateForms = document.querySelectorAll('form.cart-update-form');
    cartUpdateForms.forEach(form => {
        const newForm = form.cloneNode(true);
        form.parentNode.replaceChild(newForm, form);
        // We'll trigger submit on this form programmatically
    });

    // Quantity Increment buttons
    const incrementButtons = document.querySelectorAll('.increment-quantity');
    incrementButtons.forEach(button => {
        button.addEventListener('click', (e) => handleQuantityChange(e, 1));
    });

    // Quantity Decrement buttons
    const decrementButtons = document.querySelectorAll('.decrement-quantity');
    decrementButtons.forEach(button => {
        button.addEventListener('click', (e) => handleQuantityChange(e, -1));
    });

    // Cart Item Remove forms
    const cartRemoveForms = document.querySelectorAll('form.cart-remove-form');
    cartRemoveForms.forEach(form => {
        const newForm = form.cloneNode(true);
        form.parentNode.replaceChild(newForm, form);
        newForm.addEventListener('submit', (e) => handleCartAction(e, 'remove'));
    });

    function handleQuantityChange(e, change) {
        const button = e.target;
        const form = button.closest('form.cart-update-form');
        if (!form) return;

        const quantityInput = form.querySelector('input.cart-quantity');
        if (!quantityInput) return;

        let currentQuantity = parseInt(quantityInput.value, 10);
        currentQuantity += change;

        if (currentQuantity < 1) {
            currentQuantity = 1;
        }
        quantityInput.value = currentQuantity;

        // Automatically submit the update form
        // Create a new submit event to pass to handleCartAction
        const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
        form.dispatchEvent(submitEvent);
    }

    function handleCartAction(e, actionType) {
        e.preventDefault();
        const form = e.target;
        let submitBtn = form.querySelector('button[type="submit"]');
        
        // For quantity updates, the actual submit button is hidden, we use the +/- buttons as triggers
        // or the form submission is programmatic. We need a reference for UI feedback if needed.
        if (actionType === 'update' && !submitBtn) {
            // If there's no visible submit button for update (e.g. hidden one), 
            // we might not need to change button text, or we could target a different element for feedback.
            // For now, we'll proceed, but this might need adjustment for visual feedback.
        }

        const originalBtnText = submitBtn ? submitBtn.innerHTML : '';

        if (submitBtn) {
            let loadingText = 'Processing...';
            if (actionType === 'add') loadingText = '<i class="fas fa-spinner fa-spin"></i> Adding...';
            else if (actionType === 'update') loadingText = '<i class="fas fa-spinner fa-spin"></i> Updating...';
            else if (actionType === 'remove') loadingText = '<i class="fas fa-spinner fa-spin"></i> Removing...';
            submitBtn.innerHTML = loadingText;
            submitBtn.disabled = true;
        }

        sendCartRequest(form, submitBtn, originalBtnText, actionType);
    }

    function sendCartRequest(form, submitBtn, originalBtnText, actionType) {
        const formData = new FormData(form);
        if (csrfToken) {
            formData.append('_token', csrfToken);
        }

        // For PATCH (update), Laravel expects _method field
        if (form.method.toUpperCase() === 'POST' && (actionType === 'update' || form.querySelector('input[name="_method"]'))) {
            if (actionType === 'update' && !formData.has('_method')) {
                 formData.append('_method', 'PATCH'); // Or 'PUT' depending on your route definition
            }
        }

        const options = {
            method: form.method.toUpperCase(),
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json',
            },
            body: formData
        };
        
        fetch(form.action, options)
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => { throw {status: response.status, data: err}; });
                }
                return response.json();
            })
            .then(data => handleSuccess(data, submitBtn, originalBtnText, actionType, form))
            .catch(error => handleError(error, submitBtn, originalBtnText, actionType, form));
    }

    function handleSuccess(data, submitBtn, originalBtnText, actionType, form) {
        updateCartCount(data.cart_count || 0);
        showNotification(data.message || 'Action successful!', 'success');

        if (submitBtn) {
            let successText = '<i class="fas fa-check"></i> Done!';
            if (actionType === 'add') successText = '<i class="fas fa-check"></i> Added!';
            else if (actionType === 'update') successText = '<i class="fas fa-check"></i> Updated!';
            // For remove, the item will disappear, so button state is less critical or might be gone.
            submitBtn.innerHTML = successText;
            setTimeout(() => {
                submitBtn.innerHTML = originalBtnText;
                submitBtn.disabled = false;
            }, 1500);
        }

        if (actionType === 'remove' && data.success) {
            // Remove the item from the DOM
            const cartItemDiv = form.closest('.d-flex.p-4'); // Adjust selector if structure is different
            if (cartItemDiv) {
                cartItemDiv.style.transition = 'opacity 0.5s ease';
                cartItemDiv.style.opacity = '0';
                setTimeout(() => cartItemDiv.remove(), 500);
            }
            // Potentially update totals on the page if they are not part of a full page reload/data refresh
            if (data.cart_subtotal !== undefined && data.cart_tax !== undefined && data.cart_total !== undefined) {
                updateCartTotals(data.cart_subtotal, data.cart_tax, data.cart_total);
            }
        }
        
        if (actionType === 'update' && data.item_total_price !== undefined) {
            const priceElement = form.closest('.d-flex.p-4').querySelector('.fw-bold.mb-0'); // This selector needs to be specific to the item's total price
            if (priceElement) {
                priceElement.textContent = `₦${parseFloat(data.item_total_price).toFixed(2)}`;
            }
             if (data.cart_subtotal !== undefined && data.cart_tax !== undefined && data.cart_total !== undefined) {
                updateCartTotals(data.cart_subtotal, data.cart_tax, data.cart_total);
            }
        }
    }

    function handleError(error, submitBtn, originalBtnText, actionType, form) {
        console.error('Cart error:', error);
        let errorMessage = 'An error occurred. Please try again.';
        if (error && error.data && error.data.message) {
            errorMessage = error.data.message;
        } else if (error && error.message) {
            errorMessage = error.message;
        }
        showNotification(errorMessage, 'error');

        if (submitBtn) {
            submitBtn.innerHTML = originalBtnText;
            submitBtn.disabled = false;
        }
    }

    function updateCartCount(count) {
        const cartCountBadges = document.querySelectorAll('.cart-count, .cart-count-badge');
        cartCountBadges.forEach(badge => {
            badge.textContent = count;
            badge.style.display = count > 0 ? 'inline-block' : 'none'; // Show/hide badge
        });
    }
    
    function updateCartTotals(subtotal, tax, total) {
        const subtotalEl = document.querySelector('#cart-subtotal'); // Assuming IDs for these elements
        const taxEl = document.querySelector('#cart-tax');
        const totalEl = document.querySelector('#cart-total');

        if (subtotalEl) subtotalEl.textContent = `₦${parseFloat(subtotal).toFixed(2)}`;
        if (taxEl) taxEl.textContent = `₦${parseFloat(tax).toFixed(2)}`;
        if (totalEl) totalEl.textContent = `₦${parseFloat(total).toFixed(2)}`;
    }

    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} notification-toast`;
        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                <span>${message}</span>
            </div>
        `;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '9999';
        notification.style.minWidth = '250px';
        notification.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
        notification.style.transition = 'all 0.3s ease';
        notification.style.opacity = '0';
        notification.style.transform = 'translateY(-20px)';
        document.body.appendChild(notification);
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateY(0)';
        }, 10);
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}
