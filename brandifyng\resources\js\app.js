import './bootstrap';

// Hero Image Slider
document.addEventListener('DOMContentLoaded', () => {
    const heroSlider = {
        images: [
            'https://picsum.photos/1920/1080?random=1',
            'https://picsum.photos/1920/1080?random=2',
            'https://picsum.photos/1920/1080?random=3',
            'https://picsum.photos/1920/1080?random=4'
        ],
        currentIndex: 0,
        interval: null,
        preloadedImages: [],

        init() {
            // Preload all images
            this.preloadImages().then(() => {
                // Set initial slide
                this.updateSlide();
                // Start the interval after images are loaded
                this.interval = setInterval(() => this.nextSlide(), 5000);
            });
        },

        async preloadImages() {
            const loadPromises = this.images.map(src => {
                return new Promise((resolve, reject) => {
                    const img = new Image();
                    img.onload = () => {
                        this.preloadedImages.push(img);
                        resolve();
                    };
                    img.onerror = reject;
                    img.src = src;
                });
            });
            await Promise.all(loadPromises);
        },

        nextSlide() {
            this.currentIndex = (this.currentIndex + 1) % this.images.length;
            this.updateSlide();
        },

        updateSlide() {
            const sliderElement = document.querySelector('.hero-slider');
            if (sliderElement) {
                sliderElement.style.backgroundImage = `url(${this.images[this.currentIndex]})`;
                sliderElement.style.opacity = '0';
                // Trigger reflow
                sliderElement.offsetHeight;
                sliderElement.style.opacity = '1';
            }
        }
    };

    heroSlider.init();
});