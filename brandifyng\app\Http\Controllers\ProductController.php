<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use App\Models\Vendor;
use App\Services\ViewedProductsService;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    /**
     * The viewed products service instance.
     *
     * @var \App\Services\ViewedProductsService
     */
    protected $viewedProductsService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\ViewedProductsService  $viewedProductsService
     * @return void
     */
    public function __construct(ViewedProductsService $viewedProductsService)
    {
        $this->viewedProductsService = $viewedProductsService;
    }
    /**
     * Display a listing of the products.
     */
    public function index(Request $request)
    {
        $query = Product::query()
            ->select('products.*') // Ensure all product columns are selected
            ->with(['vendor', 'category']) // Eager load relationships (removed brand since vendors represent their own brands)
            ->where('products.is_active', true);

        // Filter by category slug (from URL or query string)
        $categorySlug = $request->category_slug ?? $request->input('category'); // Allow direct category slug from JS
        if ($categorySlug) {
            $query->whereHas('category', function($q) use ($categorySlug) {
                $q->where('slug', $categorySlug);
            });
        }

        // Filter by vendor
        if ($request->has('vendor')) {
            $query->whereHas('vendor', function($q) use ($request) {
                $q->where('slug', $request->vendor);
            });
        }

        // Filter by brand
        if ($request->has('brand')) {
            $query->whereHas('brand', function($q) use ($request) {
                $q->where('slug', $request->brand);
            });
        }

        // Filter by price range
        if ($request->has('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }

        if ($request->has('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // Sort products
        if ($request->has('sort_by')) { // Changed from 'sort' to 'sort_by' to match form
            switch ($request->sort_by) {
                case 'price_asc': // Changed from 'price_low'
                    $query->orderBy('price', 'asc');
                    break;
                case 'price_desc': // Changed from 'price_high'
                    $query->orderBy('price', 'desc');
                    break;
                case 'latest': // Changed from 'newest'
                    $query->orderBy('created_at', 'desc');
                    break;
                case 'name_asc':
                    $query->orderBy('name', 'asc');
                    break;
                case 'name_desc':
                    $query->orderBy('name', 'desc');
                    break;
                // case 'popular': // Keep if you have this logic
                //     $query->withCount('orderItems')->orderBy('order_items_count', 'desc');
                //     break;
                default:
                    $query->orderBy('created_at', 'desc');
            }
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $products = $query->paginate(12)->appends($request->except('page')); // appends existing query params
        $categories = Category::where('is_active', true)->orderBy('name')->get(); // Changed orderBy to name for consistency

        if ($request->ajax()) {
            // For AJAX requests, return only the product grid HTML and pagination info
            $productGridHtml = view('products._product_grid', compact('products'))->render();

            return response()->json([
                'html_content' => $productGridHtml,
                'query_params' => $request->all(), // Send back current query params for URL update
                // Optionally, send pagination data if you want to rebuild it client-side, though rendering it in the partial is simpler for now.
            ]);
        }

        return view('products.index', compact('products', 'categories'));
    }

    /**
     * Display the specified product.
     *
     * @param  \App\Models\Product  $product
     * @return \Illuminate\View\View
     */
    public function show(Product $product)
    {
        // Track this product view in recently viewed products
        $this->viewedProductsService->addProduct($product);
        
        // Load related products from the same category
        $relatedProducts = Product::query()
            ->select('products.*') // Ensure all product columns are selected
            ->with(['vendor', 'category']) // Eager load relationships (removed brand since vendors represent their own brands)
            ->where('products.category_id', $product->category_id)
            ->where('products.id', '!=', $product->id)
            ->where('products.is_active', true)
            ->limit(4)
            ->get();
            
        // Get recently viewed products excluding the current one
        $recentlyViewedProducts = $this->viewedProductsService->getRecentlyViewedProducts(4)
            ->filter(function($item) use ($product) {
                return $item->id !== $product->id;
            })
            ->take(3);

        return view('products.show', compact('product', 'relatedProducts', 'recentlyViewedProducts'));
    }

    /**
     * Display products by category.
     */
    public function category(Category $category)
    {
        $products = Product::query()
            ->select('products.*') // Ensure all product columns are selected
            ->with(['vendor', 'category']) // Eager load relationships (removed brand since vendors represent their own brands)
            ->where(function ($query) use ($category) {
                $query->where('products.category_id', $category->id)
                      ->orWhereHas('category', function($q) use ($category) {
                          $q->where('parent_id', $category->id);
                      });
            })
            ->where('products.is_active', true)
            ->paginate(12);

        return view('products.category', compact('category', 'products'));
    }

    /**
     * Display products by vendor.
     */
    public function vendor(Vendor $vendor)
    {
        $products = Product::query()
            ->select('products.*') // Ensure all product columns are selected
            ->with(['vendor', 'category']) // Eager load relationships (removed brand since vendors represent their own brands)
            ->where('products.vendor_id', $vendor->id)
            ->where('products.is_active', true)
            ->paginate(12);

        $categories = Category::whereHas('products', function($query) use ($vendor) {
            $query->where('vendor_id', $vendor->id);
        })
        ->withCount(['products' => function($query) use ($vendor) {
            $query->where('vendor_id', $vendor->id);
        }])
        ->get();

        return view('vendors.storefront', compact('vendor', 'products', 'categories'));
    }

    /**
     * Search for products.
     */
    public function search(Request $request)
    {
        $queryTerm = $request->input('query');

        $productsQuery = Product::query()
            ->select('products.*') // Explicitly select all columns from products table
            ->with(['vendor', 'category']) // Eager load relationships (removed brand since vendors represent their own brands)
            ->where('products.is_active', true);

        if ($queryTerm) {
            $productsQuery->where(function ($q) use ($queryTerm) {
                $q->where('products.name', 'like', "%{$queryTerm}%")
                  ->orWhere('products.description', 'like', "%{$queryTerm}%");
            });
        }

        // Filter by price range
        if ($request->filled('min_price')) {
            $productsQuery->where('products.price', '>=', $request->input('min_price'));
        }
        if ($request->filled('max_price')) {
            $productsQuery->where('products.price', '<=', $request->input('max_price'));
        }

        // Sort products
        $sortOrder = $request->input('sort', 'newest'); // Default to 'newest'
        switch ($sortOrder) {
            case 'price_low':
                $productsQuery->orderBy('products.price', 'asc');
                break;
            case 'price_high':
                $productsQuery->orderBy('products.price', 'desc');
                break;
            // Add 'popular' case if logic exists, e.g., by sales or views
            // case 'popular':
            //     $productsQuery->orderBy('some_popularity_metric', 'desc'); // Placeholder
            //     break;
            case 'newest':
            default:
                $productsQuery->orderBy('products.created_at', 'desc');
                break;
        }

        $products = $productsQuery->paginate(12)->withQueryString();

        if ($request->ajax()) {
            // For AJAX, we can return a rendered partial or JSON data
            // Returning JSON data is often more flexible for frontend frameworks
            $productsData = $products->getCollection()->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'price' => $product->price, // Or use $product->getCurrentPrice()
                    'discount_price' => $product->discount_price,
                    'image_url' => $product->image_url, // Accessor will be called here
                    'vendor_name' => optional($product->vendor)->shop_name,
                    'category_name' => optional($product->category)->name,
                    'show_url' => route('products.show', $product->slug),
                    // Add other fields as needed by your frontend
                ];
            });

            return response()->json([
                'products' => $productsData,
                'pagination' => (string) $products->links(), // Render pagination HTML
                'query' => $queryTerm,
                'total' => $products->total()
            ]);
        }

        $categories = Category::where('is_active', true)->orderBy('name')->get();
        return view('products.index', compact('products', 'queryTerm', 'categories'));
    }
}
