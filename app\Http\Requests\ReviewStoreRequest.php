<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\Order;

class ReviewStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        // Check if user has purchased this product
        $productId = $this->route('product') ? $this->route('product')->id : $this->product_id;
        
        $hasPurchased = Order::where('user_id', auth()->id())
            ->where('payment_status', 'paid')
            ->whereHas('items', function ($query) use ($productId) {
                $query->where('product_id', $productId);
            })
            ->exists();

        return $hasPurchased;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'required|string|max:1000',
            'product_id' => 'required|exists:products,id',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'rating.required' => 'Please provide a rating.',
            'rating.min' => 'Rating must be at least 1 star.',
            'rating.max' => 'Rating cannot exceed 5 stars.',
            'comment.required' => 'Please write a review comment.',
            'comment.max' => 'Review comment cannot exceed 1000 characters.',
            'product_id.required' => 'Product ID is required.',
            'product_id.exists' => 'Selected product does not exist.',
        ];
    }

    /**
     * Get custom authorization error message.
     */
    protected function failedAuthorization()
    {
        abort(403, 'You can only review products you have purchased.');
    }
}
