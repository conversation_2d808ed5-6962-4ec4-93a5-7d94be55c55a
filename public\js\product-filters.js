/**
 * Enhanced Product Filtering with AJAX
 * Provides real-time filtering without page reloads
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeProductFilters();
});

function initializeProductFilters() {
    const filterForm = document.querySelector('#product-filter-form');
    const productGrid = document.querySelector('#products-grid');
    const paginationContainer = document.querySelector('#pagination-container');
    const loadingOverlay = createLoadingOverlay();
    
    if (!filterForm || !productGrid) return;

    // Add loading overlay to product grid
    productGrid.parentNode.insertBefore(loadingOverlay, productGrid);

    // Filter inputs
    const categoryFilter = document.querySelector('#category-filter');
    const priceMinFilter = document.querySelector('#price-min');
    const priceMaxFilter = document.querySelector('#price-max');
    const sortFilter = document.querySelector('#sort-filter');
    const searchInput = document.querySelector('#search-input');

    // Debounce function for search input
    let searchTimeout;
    function debounce(func, wait) {
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(searchTimeout);
                func(...args);
            };
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(later, wait);
        };
    }

    // Event listeners
    if (categoryFilter) {
        categoryFilter.addEventListener('change', handleFilterChange);
    }
    
    if (priceMinFilter) {
        priceMinFilter.addEventListener('input', debounce(handleFilterChange, 500));
    }
    
    if (priceMaxFilter) {
        priceMaxFilter.addEventListener('input', debounce(handleFilterChange, 500));
    }
    
    if (sortFilter) {
        sortFilter.addEventListener('change', handleFilterChange);
    }
    
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleFilterChange, 300));
    }

    // Handle filter changes
    function handleFilterChange() {
        const formData = new FormData(filterForm);
        const params = new URLSearchParams();
        
        for (let [key, value] of formData.entries()) {
            if (value.trim() !== '') {
                params.append(key, value);
            }
        }

        // Show loading state
        showLoading();
        
        // Make AJAX request
        fetch(`${filterForm.action}?${params.toString()}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            updateProductGrid(data.html_content);
            updatePagination(data.pagination);
            updateURL(params);
            hideLoading();
            
            // Show results count
            showResultsCount(data.total || 0);
        })
        .catch(error => {
            console.error('Filter error:', error);
            hideLoading();
            showErrorMessage('Failed to load products. Please try again.');
        });
    }

    // Create loading overlay
    function createLoadingOverlay() {
        const overlay = document.createElement('div');
        overlay.id = 'products-loading-overlay';
        overlay.className = 'position-absolute w-100 h-100 d-flex align-items-center justify-content-center';
        overlay.style.cssText = `
            background: rgba(255, 255, 255, 0.8);
            z-index: 10;
            display: none;
            min-height: 400px;
        `;
        overlay.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-dark" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div class="mt-2 text-muted">Loading products...</div>
            </div>
        `;
        return overlay;
    }

    // Show loading state
    function showLoading() {
        loadingOverlay.style.display = 'flex';
        productGrid.style.opacity = '0.5';
    }

    // Hide loading state
    function hideLoading() {
        loadingOverlay.style.display = 'none';
        productGrid.style.opacity = '1';
    }

    // Update product grid
    function updateProductGrid(htmlContent) {
        productGrid.innerHTML = htmlContent;
        
        // Reinitialize cart functionality for new products
        if (typeof initializeCartFunctionality === 'function') {
            initializeCartFunctionality();
        }
        
        // Add fade-in animation
        productGrid.style.opacity = '0';
        setTimeout(() => {
            productGrid.style.transition = 'opacity 0.3s ease';
            productGrid.style.opacity = '1';
        }, 50);
    }

    // Update pagination
    function updatePagination(paginationHtml) {
        if (paginationContainer && paginationHtml) {
            paginationContainer.innerHTML = paginationHtml;
            
            // Add click handlers to pagination links
            const paginationLinks = paginationContainer.querySelectorAll('a[href]');
            paginationLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const url = new URL(this.href);
                    const page = url.searchParams.get('page');
                    if (page) {
                        loadPage(page);
                    }
                });
            });
        }
    }

    // Load specific page
    function loadPage(page) {
        const formData = new FormData(filterForm);
        const params = new URLSearchParams();
        
        for (let [key, value] of formData.entries()) {
            if (value.trim() !== '') {
                params.append(key, value);
            }
        }
        params.append('page', page);

        showLoading();
        
        fetch(`${filterForm.action}?${params.toString()}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            updateProductGrid(data.html_content);
            updatePagination(data.pagination);
            updateURL(params);
            hideLoading();
            
            // Scroll to top of products
            productGrid.scrollIntoView({ behavior: 'smooth', block: 'start' });
        })
        .catch(error => {
            console.error('Pagination error:', error);
            hideLoading();
        });
    }

    // Update URL without page reload
    function updateURL(params) {
        const newURL = `${window.location.pathname}?${params.toString()}`;
        window.history.pushState({}, '', newURL);
    }

    // Show results count
    function showResultsCount(count) {
        const resultsCountElement = document.querySelector('#results-count');
        if (resultsCountElement) {
            resultsCountElement.textContent = `${count} product${count !== 1 ? 's' : ''} found`;
        }
    }

    // Show error message
    function showErrorMessage(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger alert-dismissible fade show';
        errorDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        productGrid.parentNode.insertBefore(errorDiv, productGrid);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 5000);
    }

    // Clear all filters
    const clearFiltersBtn = document.querySelector('#clear-filters');
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', function() {
            filterForm.reset();
            handleFilterChange();
        });
    }

    // Price range slider (if using range inputs)
    const priceRangeInputs = document.querySelectorAll('input[type="range"]');
    priceRangeInputs.forEach(input => {
        input.addEventListener('input', function() {
            const output = document.querySelector(`#${this.id}-value`);
            if (output) {
                output.textContent = `₦${parseInt(this.value).toLocaleString()}`;
            }
        });
    });
}

// Export for use in other scripts
window.initializeProductFilters = initializeProductFilters;
