/**
 * Hero Slider - Completely Redesigned
 * Robust, reliable slider implementation
 */

// Execute when DOM is fully loaded
window.addEventListener('DOMContentLoaded', function() {
    initializeSlider();
});

function initializeSlider() {
    // Get slider elements
    const slider = document.querySelector('.hero-slider');
    
    // Exit if slider not found - CRITICAL CHECK
    if (!slider) {
        console.error('Hero slider element (.hero-slider) not found. Aborting slider initialization.');
        return;
    }

    const prevBtn = document.querySelector('.slider-prev');
    const nextBtn = document.querySelector('.slider-next');
    // dots will be queried dynamically when needed
    
    // Define images (hardcoded for reliability)
    const images = [
        '/storage/banner3.jpg', // Was 3rd, now 1st
        '/storage/banner1.jpg', // Was 1st, now 2nd
        '/storage/banner2.jpg'  // Was 2nd, now 3rd
    ];
    // Preload images for smoother transitions
    preloadImages(images);
    
    // Initialize state
    let currentIndex = 0;
    let autoplayInterval = null;
    
    // Set up initial slide
    updateSlide(currentIndex);
    startAutoplay();
    
    // Set up event listeners
    setupEventListeners();
    
    /**
     * Core Functions
     */
    
    // Update the slide display
    function updateSlide(index) {
        // Ensure index is within bounds
        currentIndex = (index + images.length) % images.length;
        
        // Update the background image
        slider.style.backgroundImage = `url(${images[currentIndex]})`;
        
        // Update the dots
        updateDots();
    }
    
    // Update the dots to reflect current slide
    function updateDots() {
        const currentDots = slider.querySelectorAll('.slider-dot'); // Query within the slider context
        currentDots.forEach(dot => {
            dot.classList.remove('active', 'bg-dark', 'bg-secondary'); 
            // Compare dot's data-index with the currentIndex
            if (parseInt(dot.dataset.index, 10) === currentIndex) {
                dot.classList.add('active', 'bg-dark');
            } else {
                dot.classList.add('bg-secondary');
            }
        });
    }
    
    // Go to next slide
    function goToNextSlide() {
        updateSlide(currentIndex + 1);
    }
    
    // Go to previous slide
    function goToPrevSlide() {
        updateSlide(currentIndex - 1);
    }
    
    // Start autoplay
    function startAutoplay() {
        stopAutoplay(); // Clear any existing interval
        autoplayInterval = setInterval(goToNextSlide, 5000);
    }
    
    // Stop autoplay
    function stopAutoplay() {
        if (autoplayInterval) {
            clearInterval(autoplayInterval);
            autoplayInterval = null;
        }
    }
    
    // Preload images
    function preloadImages(imageUrls) {
        imageUrls.forEach(url => {
            const img = new Image();
            img.src = url;
        });
    }
    
    // Set up all event listeners
    function setupEventListeners() {
        // Next button
        if (nextBtn) {
            const newNextBtn = nextBtn.cloneNode(true);
            nextBtn.parentNode.replaceChild(newNextBtn, nextBtn);
            newNextBtn.addEventListener('click', function(e) {
                e.preventDefault();
                goToNextSlide();
                restartAutoplay();
            });
        }
        
        // Previous button
        if (prevBtn) {
            const newPrevBtn = prevBtn.cloneNode(true);
            prevBtn.parentNode.replaceChild(newPrevBtn, prevBtn);
            newPrevBtn.addEventListener('click', function(e) {
                e.preventDefault();
                goToPrevSlide();
                restartAutoplay();
            });
        }
        
        // Dot navigation
        const initialDots = slider.querySelectorAll('.slider-dot'); // Query within the slider context
        initialDots.forEach((dot) => {
            const newDot = dot.cloneNode(true);
            if (dot.parentNode) { // Ensure parent exists before replacing
                dot.parentNode.replaceChild(newDot, dot);
            }
            
            newDot.addEventListener('click', function(e) {
                e.preventDefault();
                const clickedIndex = parseInt(newDot.dataset.index, 10); // Use data-index
                updateSlide(clickedIndex);
                restartAutoplay();
            });
        });
        
        // Restart autoplay helper
        function restartAutoplay() {
            stopAutoplay();
            startAutoplay();
        }
    }
}
