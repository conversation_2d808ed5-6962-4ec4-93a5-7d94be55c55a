<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\User;
use App\Models\Vendor;

class DashboardController extends Controller
{
    public function index()
    {
        $totalSales = Order::sum('total_amount');
        $totalOrders = Order::count();
        $totalCustomers = User::whereHas('role', function($q) { $q->where('name', 'customer'); })->count();
        $totalVendors = Vendor::count();
        $pendingVendors = Vendor::where('approved', false)->limit(5)->get();
        $recentOrders = Order::with(['user', 'vendor'])->latest()->limit(5)->get();

        return view('admin.dashboard', compact('totalSales', 'totalOrders', 'totalCustomers', 'totalVendors', 'pendingVendors', 'recentOrders'));
    }
}
