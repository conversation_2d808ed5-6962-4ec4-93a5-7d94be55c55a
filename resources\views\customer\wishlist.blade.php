@extends('layouts.app')

@section('content')
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-decoration-none text-dark">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}" class="text-decoration-none text-dark">My Account</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Wishlist</li>
                </ol>
            </nav>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <h1 class="fw-bold mb-4">My Wishlist</h1>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-3 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="rounded-circle bg-dark text-white d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;">
                            {{ auth()->user()->initials() }}
                        </div>
                        <div>
                            <h5 class="fw-bold mb-0">{{ auth()->user()->name }}</h5>
                            <p class="text-muted mb-0">{{ auth()->user()->email }}</p>
                        </div>
                    </div>
                    
                    <div class="list-group list-group-flush">
                        <a href="{{ route('dashboard') }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                        <a href="{{ route('orders.index') }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-shopping-bag me-2"></i> My Orders
                        </a>
                        <a href="{{ route('wishlist.index') }}" class="list-group-item list-group-item-action active">
                            <i class="fas fa-heart me-2"></i> My Wishlist
                        </a>
                        <a href="{{ route('settings.profile') }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-user-cog me-2"></i> Account Settings
                        </a>
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="list-group-item list-group-item-action text-danger">
                                <i class="fas fa-sign-out-alt me-2"></i> Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-9">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    @if(auth()->user()->wishlist()->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col" class="border-0">Product</th>
                                        <th scope="col" class="border-0">Price</th>
                                        <th scope="col" class="border-0">Stock Status</th>
                                        <th scope="col" class="border-0">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach(auth()->user()->wishlist()->with('product', 'product.category')->get() as $wishlist)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="{{ $wishlist->product->image_url ?? 'https://via.placeholder.com/80x80?text='.$wishlist->product->name }}" alt="{{ $wishlist->product->name }}" class="img-fluid rounded me-3" style="width: 80px; height: 80px; object-fit: cover;">
                                                <div>
                                                    <h6 class="fw-bold mb-1">
                                                        <a href="{{ route('products.show', $wishlist->product->slug) }}" class="text-decoration-none text-dark">{{ $wishlist->product->name }}</a>
                                                    </h6>
                                                    <p class="text-muted mb-0">{{ $wishlist->product->category->name }}</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @if($wishlist->product->isOnSale())
                                                <div class="d-flex flex-column">
                                                    <span class="fw-bold">${{ number_format($wishlist->product->discount_price, 2) }}</span>
                                                    <span class="text-muted text-decoration-line-through">${{ number_format($wishlist->product->price, 2) }}</span>
                                                </div>
                                            @else
                                                <span class="fw-bold">${{ number_format($wishlist->product->price, 2) }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($wishlist->product->stock_quantity > 0)
                                                <span class="badge bg-success">In Stock</span>
                                            @else
                                                <span class="badge bg-danger">Out of Stock</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="d-flex">
                                                <form action="{{ route('cart.add', $wishlist->product) }}" method="POST" class="me-2">
                                                    @csrf
                                                    <input type="hidden" name="quantity" value="1">
                                                    <button type="submit" class="btn btn-sm btn-dark">
                                                        <i class="fas fa-shopping-cart me-1"></i> Add to Cart
                                                    </button>
                                                </form>
                                                <form action="{{ route('wishlist.remove', $wishlist) }}" method="POST">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center p-3 bg-light">
                            <a href="{{ route('products.index') }}" class="btn btn-outline-dark">
                                <i class="fas fa-arrow-left me-2"></i> Continue Shopping
                            </a>
                            <form action="{{ route('wishlist.clear') }}" method="POST">
                                @csrf
                                <button type="submit" class="btn btn-outline-danger">
                                    <i class="fas fa-trash me-2"></i> Clear Wishlist
                                </button>
                            </form>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="far fa-heart fa-4x text-muted mb-3"></i>
                            <h4 class="fw-bold mb-2">Your wishlist is empty</h4>
                            <p class="text-muted mb-4">Add items you love to your wishlist. Review them anytime and easily move them to the cart.</p>
                            <a href="{{ route('products.index') }}" class="btn btn-dark">
                                <i class="fas fa-shopping-bag me-2"></i> Start Shopping
                            </a>
                        </div>
                    @endif
                </div>
            </div>
            
            <!-- Recently Viewed Products -->
            <div class="mt-5">
                <h3 class="fw-bold mb-4">Recently Viewed</h3>
                
                <div class="row">
                    <!-- Product Card 1 -->
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 border-2 hover-border-dark">
                            <div class="position-relative">
                                <img src="https://via.placeholder.com/300x300?text=Product+1" alt="Product 1" class="card-img-top">
                            </div>
                            <div class="card-body p-4">
                                <p class="text-muted small mb-1">Electronics</p>
                                <h5 class="fw-semibold mb-1">Wireless Headphones</h5>
                                <p class="fw-bold mb-3">$129.99</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <button class="btn btn-dark d-flex align-items-center justify-content-center">
                                        <i class="fa-solid fa-cart-plus me-2"></i> Add to Cart
                                    </button>
                                    <a href="#" class="text-dark fs-5"><i class="fas fa-heart"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Product Card 2 -->
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 border-2 hover-border-dark">
                            <div class="position-relative">
                                <img src="https://via.placeholder.com/300x300?text=Product+2" alt="Product 2" class="card-img-top">
                                <div class="position-absolute top-2 start-2 bg-danger text-white text-xs fw-bold px-2 py-1 rounded">SALE</div>
                            </div>
                            <div class="card-body p-4">
                                <p class="text-muted small mb-1">Electronics</p>
                                <h5 class="fw-semibold mb-1">Smart Watch</h5>
                                <div class="d-flex align-items-center mb-3">
                                    <p class="fw-bold me-2 mb-0">$99.99</p>
                                    <p class="text-muted text-decoration-line-through mb-0">$149.99</p>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <button class="btn btn-dark d-flex align-items-center justify-content-center">
                                        <i class="fa-solid fa-cart-plus me-2"></i> Add to Cart
                                    </button>
                                    <a href="#" class="text-dark fs-5"><i class="far fa-heart"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Product Card 3 -->
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 border-2 hover-border-dark">
                            <div class="position-relative">
                                <img src="https://via.placeholder.com/300x300?text=Product+3" alt="Product 3" class="card-img-top">
                            </div>
                            <div class="card-body p-4">
                                <p class="text-muted small mb-1">Electronics</p>
                                <h5 class="fw-semibold mb-1">Bluetooth Speaker</h5>
                                <p class="fw-bold mb-3">$59.99</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <button class="btn btn-dark d-flex align-items-center justify-content-center">
                                        <i class="fa-solid fa-cart-plus me-2"></i> Add to Cart
                                    </button>
                                    <a href="#" class="text-dark fs-5"><i class="far fa-heart"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
