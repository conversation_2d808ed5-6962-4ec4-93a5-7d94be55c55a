@extends('layouts.admin')
@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="fw-bold">Edit Subscription</h2>
        <a href="{{ route('admin.subscriptions.index') }}" class="btn btn-outline-dark">Back</a>
    </div>
    <div class="card">
        <div class="card-body">
            <form method="POST" action="{{ route('admin.subscriptions.update', $subscription) }}">
                @csrf
                @method('PUT')
                <div class="mb-3">
                    <label for="plan_id" class="form-label">Plan</label>
                    <select class="form-select" id="plan_id" name="plan_id" required>
                        @foreach($plans as $plan)
                            <option value="{{ $plan->id }}" @if($subscription->plan_id == $plan->id) selected @endif>{{ $plan->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="mb-3">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ $subscription->end_date ? date('Y-m-d', strtotime($subscription->end_date)) : '' }}" required>
                </div>
                <div class="mb-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status" required>
                        <option value="active" @if($subscription->status == 'active') selected @endif>Active</option>
                        <option value="expired" @if($subscription->status == 'expired') selected @endif>Expired</option>
                        <option value="canceled" @if($subscription->status == 'canceled') selected @endif>Canceled</option>
                        <option value="pending" @if($subscription->status == 'pending') selected @endif>Pending</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">Update Subscription</button>
            </form>
        </div>
    </div>
</div>
@endsection
