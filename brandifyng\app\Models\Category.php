<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory;
    protected $fillable = [
        'name',
        'slug',
        'parent_id',
    ];

    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id');
    }
    
    public function products()
    {
        return $this->hasMany(Product::class);
    }
    
    public function getRouteKeyName()
    {
        return 'slug';
    }
    
    public function getAllProducts()
    {
        $products = $this->products()->where('is_active', true);
        
        // Get products from child categories as well
        foreach ($this->children as $child) {
            $products = $products->union($child->getAllProducts());
        }
        
        return $products;
    }
}
