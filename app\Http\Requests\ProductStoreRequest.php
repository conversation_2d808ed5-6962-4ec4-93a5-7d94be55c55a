<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProductStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->isVendor();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'required|string|max:2000',
            'price' => 'required|numeric|min:0.01',
            'discount_price' => 'nullable|numeric|min:0|lt:price',
            'stock_quantity' => 'required|integer|min:0',
            'category_id' => 'required|exists:categories,id',
            'brand_id' => 'nullable|exists:brands,id',
            'image_url' => 'nullable|image|mimes:jpg,jpeg,png,gif|max:2048',
            'is_active' => 'boolean',
            'weight' => 'nullable|numeric|min:0.001',
            'length' => 'nullable|numeric|min:0.1',
            'width' => 'nullable|numeric|min:0.1',
            'height' => 'nullable|numeric|min:0.1',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Product name is required.',
            'description.required' => 'Product description is required.',
            'price.required' => 'Product price is required.',
            'price.min' => 'Product price must be greater than zero.',
            'discount_price.lt' => 'Discount price must be less than the regular price.',
            'stock_quantity.required' => 'Stock quantity is required.',
            'stock_quantity.min' => 'Stock quantity cannot be negative.',
            'category_id.required' => 'Please select a product category.',
            'category_id.exists' => 'Selected category does not exist.',
            'weight.min' => 'Weight must be at least 0.001 kg.',
            'length.min' => 'Length must be at least 0.1 cm.',
            'width.min' => 'Width must be at least 0.1 cm.',
            'height.min' => 'Height must be at least 0.1 cm.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // Set default dimensions if not provided
        if (!$this->weight) {
            $this->merge(['weight' => config('ecommerce.shipping.default_weight', 1.0)]);
        }
        
        if (!$this->length) {
            $this->merge(['length' => config('ecommerce.shipping.default_dimensions.length', 20)]);
        }
        
        if (!$this->width) {
            $this->merge(['width' => config('ecommerce.shipping.default_dimensions.width', 15)]);
        }
        
        if (!$this->height) {
            $this->merge(['height' => config('ecommerce.shipping.default_dimensions.height', 10)]);
        }
    }
}
