<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Product;
use Illuminate\Support\Str;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all users with customer role and vendors
        $customers = User::where('role_id', 3)->get(); // Assuming role_id 3 is customer
        $vendors = Vendor::all();
        $products = Product::all();

        if ($customers->isEmpty() || $vendors->isEmpty() || $products->isEmpty()) {
            // Create some customer users if none exist
            if ($customers->isEmpty()) {
                for ($i = 1; $i <= 10; $i++) {
                    $customers->push(User::create([
                        'name' => 'Customer ' . $i,
                        'email' => 'customer' . $i . '@example.com',
                        'password' => bcrypt('password'),
                        'role_id' => 3,
                        'email_verified_at' => now(),
                    ]));
                }
            }
        }

        // Nigerian states and major cities for realistic location data
        $nigerianLocations = [
            ['state' => 'Lagos', 'city' => 'Lagos', 'lat' => 6.5244, 'lng' => 3.3792],
            ['state' => 'Lagos', 'city' => 'Ikeja', 'lat' => 6.6018, 'lng' => 3.3515],
            ['state' => 'Abuja', 'city' => 'Abuja', 'lat' => 9.0765, 'lng' => 7.3986],
            ['state' => 'Kano', 'city' => 'Kano', 'lat' => 12.0022, 'lng' => 8.5920],
            ['state' => 'Rivers', 'city' => 'Port Harcourt', 'lat' => 4.8156, 'lng' => 7.0498],
            ['state' => 'Oyo', 'city' => 'Ibadan', 'lat' => 7.3775, 'lng' => 3.9470],
            ['state' => 'Kaduna', 'city' => 'Kaduna', 'lat' => 10.5105, 'lng' => 7.4165],
            ['state' => 'Anambra', 'city' => 'Awka', 'lat' => 6.2120, 'lng' => 7.0740],
            ['state' => 'Plateau', 'city' => 'Jos', 'lat' => 9.8965, 'lng' => 8.8583],
            ['state' => 'Cross River', 'city' => 'Calabar', 'lat' => 4.9517, 'lng' => 8.3220],
            ['state' => 'Enugu', 'city' => 'Enugu', 'lat' => 6.5244, 'lng' => 7.5086],
            ['state' => 'Ondo', 'city' => 'Akure', 'lat' => 7.2571, 'lng' => 5.2058],
            ['state' => 'Osun', 'city' => 'Osogbo', 'lat' => 7.7719, 'lng' => 4.5407],
            ['state' => 'Delta', 'city' => 'Asaba', 'lat' => 6.1968, 'lng' => 6.6963],
            ['state' => 'Edo', 'city' => 'Benin City', 'lat' => 6.3350, 'lng' => 5.6037],
        ];

        $orderStatuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled'];
        $paymentStatuses = ['unpaid', 'paid', 'failed', 'refunded'];

        // Create 100 sample orders
        for ($i = 1; $i <= 100; $i++) {
            $customer = $customers->random();
            $vendor = $vendors->random();
            $location = $nigerianLocations[array_rand($nigerianLocations)];
            
            // Create order
            $order = Order::create([
                'order_number' => 'ORD-' . strtoupper(Str::random(8)),
                'user_id' => $customer->id,
                'vendor_id' => $vendor->id,
                'total_amount' => 0, // Will be calculated after adding items
                'status' => $orderStatuses[array_rand($orderStatuses)],
                'payment_status' => $paymentStatuses[array_rand($paymentStatuses)],
                'shipping_address' => $location['city'] . ', ' . $location['state'] . ' State, Nigeria',
                'billing_address' => $location['city'] . ', ' . $location['state'] . ' State, Nigeria',
                'shipping_city' => $location['city'],
                'shipping_state' => $location['state'],
                'shipping_country' => 'Nigeria',
                'payment_gateway_reference' => 'PAY-' . strtoupper(Str::random(10)),
                'created_at' => now()->subDays(rand(1, 90)), // Random date in last 90 days
            ]);

            // Add 1-5 random products to each order
            $numItems = rand(1, 5);
            $totalAmount = 0;
            
            for ($j = 0; $j < $numItems; $j++) {
                $product = $products->random();
                $quantity = rand(1, 3);
                $priceAtPurchase = $product->discount_price > 0 ? $product->discount_price : $product->price;
                
                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'price_at_purchase' => $priceAtPurchase,
                ]);
                
                $totalAmount += $priceAtPurchase * $quantity;
            }
            
            // Update order total
            $order->update(['total_amount' => $totalAmount]);
        }
    }
}
