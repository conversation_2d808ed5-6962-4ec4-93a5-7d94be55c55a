@extends('layouts.admin')
@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="fw-bold">Edit Vendor</h2>
        <a href="{{ route('admin.vendors.index') }}" class="btn btn-outline-dark">Back</a>
    </div>
    <div class="card">
        <div class="card-body">
            <form method="POST" action="{{ route('admin.vendors.update', $vendor) }}">
                @csrf
                @method('PUT')
                <div class="mb-3">
                    <label for="shop_name" class="form-label">Shop Name</label>
                    <input type="text" class="form-control" id="shop_name" name="shop_name" value="{{ $vendor->shop_name }}" required>
                </div>
                <div class="mb-3">
                    <label for="slug" class="form-label">Slug</label>
                    <input type="text" class="form-control" id="slug" name="slug" value="{{ $vendor->slug }}" required>
                </div>
                <div class="mb-3">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="form-control" id="description" name="description">{{ $vendor->description }}</textarea>
                </div>
                <div class="mb-3">
                    <label for="address" class="form-label">Address</label>
                    <input type="text" class="form-control" id="address" name="address" value="{{ $vendor->address }}">
                </div>
                <div class="mb-3">
                    <label for="city" class="form-label">City</label>
                    <input type="text" class="form-control" id="city" name="city" value="{{ $vendor->city }}">
                </div>
                <div class="mb-3">
                    <label for="state" class="form-label">State</label>
                    <input type="text" class="form-control" id="state" name="state" value="{{ $vendor->state }}">
                </div>
                <div class="mb-3">
                    <label for="country" class="form-label">Country</label>
                    <input type="text" class="form-control" id="country" name="country" value="{{ $vendor->country }}">
                </div>
                <div class="mb-3">
                    <label for="logo" class="form-label">Logo URL</label>
                    <input type="text" class="form-control" id="logo" name="logo" value="{{ $vendor->logo }}">
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="is_approved" name="is_approved" value="1" @if($vendor->is_approved) checked @endif>
                    <label class="form-check-label" for="is_approved">Approved</label>
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="is_featured" name="is_featured" value="1" @if($vendor->is_featured) checked @endif>
                    <label class="form-check-label" for="is_featured">Featured on Homepage</label>
                </div>
                <button type="submit" class="btn btn-primary">Update Vendor</button>
            </form>
        </div>
    </div>
</div>
@endsection
