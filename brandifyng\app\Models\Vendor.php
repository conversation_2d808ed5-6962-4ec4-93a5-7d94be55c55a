<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Vendor extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_id',
        'shop_name',
        'slug',
        'description',
        'brand_description',
        'address',
        'city',
        'state',
        'country',
        'logo',
        'brand_logo',
        'is_approved',
        'is_featured',
        'subscription_status',
        'orders_processed',
        'free_order_limit',
        'subscription_required',
        'subscription_started_at',
        'subscription_expires_at',
        'monthly_subscription_fee',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    public function products()
    {
        return $this->hasMany(Product::class);
    }
    
    public function commissions()
    {
        return $this->hasMany(Commission::class);
    }
    
    public function subscription()
    {
        return $this->hasOneThrough(
            Subscription::class,
            User::class,
            'id', // Foreign key on users table...
            'user_id', // Foreign key on subscriptions table...
            'user_id', // Local key on vendors table...
            'id' // Local key on users table...
        );
    }
    
    public function getRouteKeyName()
    {
        return 'slug';
    }
    
    public function getTotalSalesAttribute()
    {
        return $this->products()
            ->withSum('orderItems as total_sales', 'price_at_purchase * quantity')
            ->get()
            ->sum('total_sales');
    }

    /**
     * Get brand description (use brand_description if available, otherwise description)
     */
    public function getBrandDescriptionAttribute(): string
    {
        return $this->attributes['brand_description'] ?? $this->description;
    }

    /**
     * Get brand logo (use brand_logo if available, otherwise logo)
     */
    public function getBrandLogoAttribute(): string
    {
        return $this->attributes['brand_logo'] ?? $this->logo;
    }
}
