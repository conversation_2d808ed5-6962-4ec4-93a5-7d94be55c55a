<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckVendorSubscription
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only check for authenticated vendor users
        if (!auth()->check() || !auth()->user()->isVendor()) {
            return $next($request);
        }

        $vendor = auth()->user()->vendor;
        
        // If vendor doesn't exist, continue
        if (!$vendor) {
            return $next($request);
        }

        // Check if vendor needs subscription for order-related actions
        if ($this->isOrderRelatedRoute($request) && $vendor->needsSubscription()) {
            // If it's an AJAX request, return JSON response
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Subscription required to process more orders.',
                    'subscription_required' => true,
                    'redirect_url' => route('vendor.subscription.plans')
                ], 402); // Payment Required
            }
            
            // For regular requests, redirect to subscription page
            return redirect()->route('vendor.subscription.plans')
                           ->with('warning', 'You have reached your free order limit. Please subscribe to continue processing orders.');
        }

        return $next($request);
    }

    /**
     * Check if the current route is order-related
     */
    private function isOrderRelatedRoute(Request $request): bool
    {
        $orderRelatedRoutes = [
            'vendor.orders.update',
            'vendor.orders.ship',
            'vendor.orders.complete',
            'vendor.products.store',
            'vendor.products.update',
        ];

        $currentRoute = $request->route()->getName();
        
        return in_array($currentRoute, $orderRelatedRoutes) || 
               str_contains($currentRoute, 'order') ||
               str_contains($currentRoute, 'product');
    }
}
