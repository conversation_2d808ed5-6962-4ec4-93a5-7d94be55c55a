@extends('layouts.admin')
@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="fw-bold">Edit Subscription Plan</h2>
        <a href="{{ route('admin.subscription-plans.index') }}" class="btn btn-outline-dark">Back</a>
    </div>
    
    @if ($errors->any())
    <div class="alert alert-danger">
        <ul class="mb-0">
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
    @endif

    <div class="card">
        <div class="card-body">
            <form method="POST" action="{{ route('admin.subscription-plans.update', $plan->id) }}">
                @csrf
                @method('PUT')
                <div class="mb-3">
                    <label for="name" class="form-label">Plan Name</label>
                    <input type="text" class="form-control" id="name" name="name" value="{{ old('name', $plan->name) }}" required>
                </div>
                
                <div class="mb-3">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="form-control" id="description" name="description" rows="3" required>{{ old('description', $plan->description) }}</textarea>
                </div>
                
                <div class="mb-3">
                    <label for="price" class="form-label">Price ($)</label>
                    <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" value="{{ old('price', $plan->price) }}" required>
                </div>
                
                <div class="mb-3">
                    <label for="duration_days" class="form-label">Duration (days)</label>
                    <input type="number" class="form-control" id="duration_days" name="duration_days" min="1" value="{{ old('duration_days', $plan->duration_days) }}" required>
                </div>
                
                <div class="mb-3">
                    <label for="features" class="form-label">Features</label>
                    <textarea class="form-control" id="features" name="features" rows="4" placeholder="Enter features, one per line">{{ old('features', $plan->features) }}</textarea>
                    <small class="text-muted">Enter each feature on a new line</small>
                </div>
                
                <div class="mb-3">
                    <label for="product_limit" class="form-label">Product Limit</label>
                    <input type="number" class="form-control" id="product_limit" name="product_limit" min="0" value="{{ old('product_limit', $plan->product_limit) }}">
                    <small class="text-muted">Leave empty for unlimited products</small>
                </div>
                
                <div class="mb-3">
                    <label for="commission_rate" class="form-label">Commission Rate (%)</label>
                    <input type="number" class="form-control" id="commission_rate" name="commission_rate" step="0.01" min="0" max="100" value="{{ old('commission_rate', $plan->commission_rate) }}">
                </div>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" {{ old('is_active', $plan->is_active) ? 'checked' : '' }}>
                    <label class="form-check-label" for="is_active">Active</label>
                </div>
                
                <button type="submit" class="btn btn-primary">Update Plan</button>
            </form>
        </div>
    </div>
</div>
@endsection
