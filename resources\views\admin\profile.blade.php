@extends('layouts.admin')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Admin Profile</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-4 mb-md-0">
                            <div class="text-center">
                                <div class="mb-3">
                                    @if(auth()->user()->profile_photo_path)
                                        <img src="{{ Storage::url(auth()->user()->profile_photo_path) }}" alt="{{ auth()->user()->name }}" class="img-fluid rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                                    @else
                                        <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center mx-auto text-white" style="width: 150px; height: 150px; font-size: 48px;">
                                            {{ auth()->user()->initials() }}
                                        </div>
                                    @endif
                                </div>
                                <h5 class="fw-bold">{{ auth()->user()->name }}</h5>
                                <p class="text-muted">Administrator</p>
                                <p><i class="fas fa-envelope me-2"></i>{{ auth()->user()->email }}</p>
                                <p><i class="fas fa-calendar me-2"></i>Member since {{ auth()->user()->created_at->format('M Y') }}</p>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Update Profile Information</h6>
                                    </div>
                                    <div class="card-body">
                                        <form action="{{ route('settings.profile') }}" method="GET">
                                            <button type="submit" class="btn btn-dark">
                                                <i class="fas fa-user-edit me-2"></i>Edit Profile Information
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Update Password</h6>
                                    </div>
                                    <div class="card-body">
                                        <form action="{{ route('settings.password') }}" method="GET">
                                            <button type="submit" class="btn btn-dark">
                                                <i class="fas fa-lock me-2"></i>Change Password
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Quick Actions</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-flex flex-wrap gap-2">
                                            <a href="{{ route('admin.dashboard') }}" class="btn btn-dark">
                                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                            </a>
                                            <a href="{{ route('admin.settings.index') }}" class="btn btn-dark">
                                                <i class="fas fa-cog me-2"></i>System Settings
                                            </a>
                                            <form action="{{ route('logout') }}" method="POST" class="d-inline">
                                                @csrf
                                                <button type="submit" class="btn btn-outline-danger">
                                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
