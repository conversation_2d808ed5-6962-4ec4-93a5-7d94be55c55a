<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class SettingsController extends Controller
{
    /**
     * Display the vendor settings page.
     */
    public function index()
    {
        return view('vendor.settings.index');
    }

    /**
     * Update the vendor settings.
     */
    public function update(Request $request)
    {
        $vendor = auth()->user()->vendor;

        $request->validate([
            'shop_name' => 'required|string|max:255',
            'slug' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-z0-9-]+$/',
                Rule::unique('vendors', 'slug')->ignore($vendor->id)
            ],
            'description' => 'required|string|max:1000',
            'brand_description' => 'nullable|string|max:1000',
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'country' => 'required|string|max:100',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'brand_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $updateData = [
            'shop_name' => $request->shop_name,
            'slug' => $request->slug,
            'description' => $request->description,
            'brand_description' => $request->brand_description,
            'address' => $request->address,
            'city' => $request->city,
            'state' => $request->state,
            'country' => $request->country,
        ];

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($vendor->logo) {
                Storage::disk('public')->delete($vendor->logo);
            }
            
            $logoPath = $request->file('logo')->store('vendor-logos', 'public');
            $updateData['logo'] = $logoPath;
        }

        // Handle brand logo upload
        if ($request->hasFile('brand_logo')) {
            // Delete old brand logo if exists
            if ($vendor->brand_logo) {
                Storage::disk('public')->delete($vendor->brand_logo);
            }
            
            $brandLogoPath = $request->file('brand_logo')->store('vendor-brand-logos', 'public');
            $updateData['brand_logo'] = $brandLogoPath;
        }

        $vendor->update($updateData);

        return redirect()->route('vendor.settings.index')
                        ->with('success', 'Shop settings updated successfully!');
    }
}
