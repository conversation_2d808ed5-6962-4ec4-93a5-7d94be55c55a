@extends('layouts.admin')
@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="fw-bold">Commissions</h2>
    </div>
    <div class="card">
        <div class="card-body p-0">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Vendor</th>
                        <th>Order ID</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($commissions as $commission)
                    <tr>
                        <td>{{ $commission->vendor->shop_name ?? '' }}</td>
                        <td>{{ $commission->order_id }}</td>
                        <td>${{ number_format($commission->amount, 2) }}</td>
                        <td>
                            @if($commission->status === 'pending')
                                <span class="badge bg-warning text-dark">Pending</span>
                            @elseif($commission->status === 'paid')
                                <span class="badge bg-success">Paid</span>
                            @else
                                <span class="badge bg-secondary">{{ ucfirst($commission->status) }}</span>
                            @endif
                        </td>
                        <td>
                            <form action="{{ route('admin.commissions.update', $commission) }}" method="POST" class="d-inline">
                                @csrf
                                @method('PUT')
                                <select name="status" class="form-select form-select-sm d-inline w-auto align-middle">
                                    <option value="pending" @if($commission->status=='pending') selected @endif>Pending</option>
                                    <option value="paid" @if($commission->status=='paid') selected @endif>Paid</option>
                                </select>
                                <button type="submit" class="btn btn-sm btn-dark ms-2">Update</button>
                            </form>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
    <div class="mt-3">
        {{ $commissions->links() }}
    </div>
</div>
@endsection
