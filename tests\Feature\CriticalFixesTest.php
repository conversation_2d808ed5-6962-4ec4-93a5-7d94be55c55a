<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CriticalFixesTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->createTestData();
    }

    private function createTestData()
    {
        // Create a category
        $this->category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
        ]);

        // Create a user and vendor
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'vendor'
        ]);

        $this->vendor = Vendor::create([
            'user_id' => $this->user->id,
            'shop_name' => 'Test Shop',
            'slug' => 'test-shop',
            'description' => 'Test shop description',
            'address' => 'Test Address',
            'city' => 'Lagos',
            'state' => 'Lagos',
            'country' => 'Nigeria',
            'is_approved' => true,
            'orders_processed' => 5,
            'free_order_limit' => 10,
        ]);

        // Create a product without brand_id
        $this->product = Product::create([
            'vendor_id' => $this->vendor->id,
            'category_id' => $this->category->id,
            'name' => 'Test Product',
            'slug' => 'test-product',
            'description' => 'Test product description',
            'price' => 1000.00,
            'stock_quantity' => 10,
            'is_active' => true,
        ]);
    }

    /** @test */
    public function product_can_be_created_without_brand_relationship()
    {
        $this->assertInstanceOf(Product::class, $this->product);
        $this->assertNull($this->product->brand_id ?? null);
        $this->assertEquals($this->vendor->id, $this->product->vendor_id);
    }

    /** @test */
    public function product_uses_vendor_as_brand()
    {
        $this->assertEquals($this->vendor->shop_name, $this->product->vendor->shop_name);
        $this->assertNotNull($this->product->vendor);
    }

    /** @test */
    public function vendor_settings_page_loads_successfully()
    {
        $response = $this->actingAs($this->user)
                        ->get(route('vendor.settings.index'));

        $response->assertStatus(200);
        $response->assertViewIs('vendor.settings.index');
    }

    /** @test */
    public function search_functionality_works_without_errors()
    {
        $response = $this->get(route('products.search', ['query' => 'test']));

        $response->assertStatus(200);
        $response->assertViewIs('products.search');
        $response->assertViewHas('products');
        $response->assertViewHas('query');
    }

    /** @test */
    public function products_index_includes_vendors_variable()
    {
        $response = $this->get(route('products.index'));

        $response->assertStatus(200);
        $response->assertViewIs('products.index');
        $response->assertViewHas('vendors');
        $response->assertViewHas('products');
        $response->assertViewHas('categories');
    }

    /** @test */
    public function vendor_subscription_system_works()
    {
        // Test that vendor needs subscription after reaching limit
        $this->vendor->update(['orders_processed' => 15]); // Exceed free limit
        $this->assertTrue($this->vendor->needsSubscription());

        // Test subscription activation
        $this->vendor->activateSubscription(30);
        $this->assertTrue($this->vendor->hasActiveSubscription());
        $this->assertFalse($this->vendor->needsSubscription());
    }

    /** @test */
    public function subscription_plans_page_loads()
    {
        $response = $this->actingAs($this->user)
                        ->get(route('vendor.subscription.plans'));

        $response->assertStatus(200);
        $response->assertViewIs('vendor.subscription.plans');
    }

    /** @test */
    public function ajax_product_search_returns_json()
    {
        $response = $this->get(route('products.search', ['query' => 'test']), [
            'X-Requested-With' => 'XMLHttpRequest',
            'Accept' => 'application/json'
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'products' => true,
            'total' => true,
        ]);
    }

    /** @test */
    public function ajax_product_filtering_works()
    {
        $response = $this->get(route('products.index', ['min_price' => 500]), [
            'X-Requested-With' => 'XMLHttpRequest',
            'Accept' => 'application/json'
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'html_content',
            'query_params',
            'total',
            'pagination'
        ]);
    }

    /** @test */
    public function currency_symbol_is_correct()
    {
        $this->assertEquals('₦', config('brandify.currency.symbol'));
    }

    /** @test */
    public function vendor_can_update_settings()
    {
        $updateData = [
            'shop_name' => 'Updated Shop Name',
            'slug' => 'updated-shop',
            'description' => 'Updated description',
            'address' => 'Updated Address',
            'city' => 'Abuja',
            'state' => 'FCT',
            'country' => 'Nigeria',
        ];

        $response = $this->actingAs($this->user)
                        ->put(route('vendor.settings.update'), $updateData);

        $response->assertRedirect(route('vendor.settings.index'));
        $response->assertSessionHas('success');

        $this->vendor->refresh();
        $this->assertEquals('Updated Shop Name', $this->vendor->shop_name);
        $this->assertEquals('updated-shop', $this->vendor->slug);
    }

    /** @test */
    public function freemium_subscription_model_works()
    {
        // New vendor should have free orders available
        $newVendor = Vendor::create([
            'user_id' => User::factory()->create()->id,
            'shop_name' => 'New Shop',
            'slug' => 'new-shop',
            'description' => 'New shop',
            'address' => 'Address',
            'city' => 'Lagos',
            'state' => 'Lagos',
            'country' => 'Nigeria',
            'is_approved' => true,
            'orders_processed' => 0,
            'free_order_limit' => 10,
        ]);

        $this->assertFalse($newVendor->needsSubscription());
        $this->assertEquals(10, $newVendor->getRemainingFreeOrders());

        // After processing 10 orders, should need subscription
        $newVendor->update(['orders_processed' => 10]);
        $this->assertTrue($newVendor->needsSubscription());
        $this->assertEquals(0, $newVendor->getRemainingFreeOrders());
    }
}
