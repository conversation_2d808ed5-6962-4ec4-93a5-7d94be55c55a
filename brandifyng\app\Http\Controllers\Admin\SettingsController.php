<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class SettingsController extends Controller
{
    public function index()
    {
        $settings = [
            'site_name' => config('app.name'),
            'contact_email' => config('mail.from.address'),
            'support_phone' => config('settings.support_phone', ''),
        ];
        return view('admin.settings.index', compact('settings'));
    }

    public function update(Request $request)
    {
        $data = $request->validate([
            'site_name' => 'required',
            'contact_email' => 'required|email',
            'support_phone' => 'nullable',
        ]);
        
        // Save settings to database or file
        $this->saveSettings($data);
        
        return redirect()->route('admin.settings.index')->with('success', 'Settings updated successfully.');
    }
    
    /**
     * Save settings to the database or env file
     *
     * @param array $data
     * @return void
     */
    private function saveSettings(array $data)
    {
        // First try to update the .env file for app name and email
        $this->updateEnvFile([
            'APP_NAME' => $data['site_name'],
            'MAIL_FROM_ADDRESS' => $data['contact_email'],
        ]);
        
        // For other settings, store in the database
        // If you don't have a settings table, you can use Laravel's cache system
        cache()->forever('settings.support_phone', $data['support_phone']);
    }
    
    /**
     * Update the .env file with new values
     *
     * @param array $data
     * @return void
     */
    private function updateEnvFile(array $data)
    {
        $envFile = app()->environmentFilePath();
        $contents = file_get_contents($envFile);
        
        foreach ($data as $key => $value) {
            // If value contains spaces, wrap in quotes
            if (strpos($value, ' ') !== false) {
                $value = '"' . $value . '"';
            }
            
            // Replace existing values
            if (preg_match("/^{$key}=(.*)/m", $contents)) {
                $contents = preg_replace("/^{$key}=(.*)/m", "{$key}={$value}", $contents);
            } else {
                // Add new values
                $contents .= "\n{$key}={$value}\n";
            }
        }
        
        file_put_contents($envFile, $contents);
    }
}
