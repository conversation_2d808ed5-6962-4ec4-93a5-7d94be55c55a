<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use App\Models\Vendor;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use App\Mail\ContactFormMail;

class HomeController extends Controller
{
    /**
     * Display the home page.
     */
    public function index()
    {
        // Get featured products
        $featuredProducts = Product::query()
            ->select('products.*') // Ensure all product columns are selected
            ->with(['category', 'vendor']) // Eager load relationships (removed brand since vendors are now brands)
            ->where('products.is_featured', true)
            ->where('products.is_active', true)
            ->orderBy('products.created_at', 'desc')
            ->limit(8)
            ->get();

        // Get new arrivals
        $newArrivals = Product::query()
            ->select('products.*') // Ensure all product columns are selected
            ->with(['category', 'vendor']) // Eager load relationships (removed brand since vendors are now brands)
            ->where('products.is_active', true)
            ->orderBy('products.created_at', 'desc')
            ->limit(4)
            ->get();

        // Get featured categories
        $categories = Category::where('is_active', true)
            ->whereNull('parent_id')
            ->orderBy('order')
            ->limit(3)
            ->get();

        // Get featured vendors (which are now also the brands)
        $featuredVendors = Vendor::where('approved', true)
            ->where('is_featured', true)
            ->withCount('products')
            ->orderBy('created_at', 'desc')
            ->limit(6)
            ->get();

        // If no featured vendors, get the most recent approved vendors
        if ($featuredVendors->isEmpty()) {
            $featuredVendors = Vendor::where('approved', true)
                ->withCount('products')
                ->orderBy('created_at', 'desc')
                ->limit(6)
                ->get();
        }

        return view('welcome-bw', compact('featuredProducts', 'newArrivals', 'categories', 'featuredVendors'));
    }

    /**
     * Handle contact form submission.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function handleContactForm(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|min:10',
        ]);

        if ($validator->fails()) {
            return redirect()->route('contact')
                        ->withErrors($validator)
                        ->withInput();
        }

        $validatedData = $validator->validated();

        try {
            // Send the email
            // IMPORTANT: Replace '<EMAIL>' with your actual admin email address
            // or use a configuration value like config('mail.admin_address')
            Mail::to('<EMAIL>')->send(new ContactFormMail(
                $validatedData['name'],
                $validatedData['email'],
                $validatedData['subject'],
                $validatedData['message']
            ));

            return redirect()->route('contact')->with('success', 'Thank you for your message! We will get back to you soon.');

        } catch (\Exception $e) {
            // Log the error or handle it as needed
            // For now, redirect back with a generic error message
            // You might want to log $e->getMessage() for debugging
            report($e); // Helper function to log exceptions
            return redirect()->route('contact')
                        ->with('error', 'Sorry, there was an issue sending your message. Please try again later.')
                        ->withInput();
        }
    }
}
