<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Hash;

class ProfileController extends Controller
{
    /**
     * Display the vendor profile.
     */
    public function index()
    {
        $vendor = auth()->user()->vendor;
        
        if (!$vendor) {
            return redirect()->route('vendor.dashboard')->with('error', 'Vendor profile not found');
        }
        
        return view('vendor.profile.index', compact('vendor'));
    }
    
    /**
     * Update the vendor profile.
     */
    public function update(Request $request)
    {
        $user = auth()->user();
        $vendor = $user->vendor;
        
        if (!$vendor) {
            return redirect()->route('vendor.dashboard')->with('error', 'Vendor profile not found');
        }
        
        // Validate the request
        $request->validate([
            'name' => 'required|string|max:255',
            'business_name' => 'required|string|max:255',
            'business_address' => 'required|string|max:500',
            'business_description' => 'required|string|max:1000',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|max:255|unique:users,email,' . $user->id,
            'logo' => 'nullable|image|mimes:jpg,jpeg,png,gif|max:2048',
            'current_password' => 'nullable|required_with:password',
            'password' => 'nullable|min:8|confirmed',
        ]);
        
        // Update user information
        $user->name = $request->name;
        $user->email = $request->email;
        
        // Update password if provided
        if ($request->filled('password')) {
            if (!Hash::check($request->current_password, $user->password)) {
                return back()->withErrors(['current_password' => 'The provided password does not match your current password.']);
            }
            
            $user->password = Hash::make($request->password);
        }
        
        $user->save();
        
        // Process logo if uploaded
        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($vendor->logo && Storage::exists($vendor->logo)) {
                Storage::delete($vendor->logo);
            }
            
            $logoPath = $request->file('logo')->store('public/vendor/logos');
            $vendor->logo = Storage::url($logoPath);
        }
        
        // Update vendor information
        $vendor->business_name = $request->business_name;
        $vendor->business_address = $request->business_address;
        $vendor->business_description = $request->business_description;
        $vendor->phone = $request->phone;
        
        if ($request->filled('bank_name')) {
            $vendor->bank_name = $request->bank_name;
        }
        
        if ($request->filled('bank_account_name')) {
            $vendor->bank_account_name = $request->bank_account_name;
        }
        
        if ($request->filled('bank_account_number')) {
            $vendor->bank_account_number = $request->bank_account_number;
        }
        
        if ($request->filled('tax_id')) {
            $vendor->tax_id = $request->tax_id;
        }
        
        if ($request->filled('paypal_email')) {
            $vendor->paypal_email = $request->paypal_email;
        }
        
        $vendor->save();
        
        return redirect()->route('vendor.profile')->with('success', 'Profile updated successfully');
    }
}
