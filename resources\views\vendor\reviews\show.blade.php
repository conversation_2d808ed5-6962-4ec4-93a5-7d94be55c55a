@extends('layouts.vendor')

@section('title', 'Respond to Review')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="fw-bold">Respond to Review</h2>
                <a href="{{ route('vendor.reviews.index') }}" class="btn btn-outline-dark">
                    <i class="fas fa-arrow-left me-1"></i>Back to Reviews
                </a>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="row">
                <div class="col-md-8">
                    <!-- Review Details -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Customer Review</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-3">
                                <h6 class="fw-bold">{{ $review->user->name }}</h6>
                                <div>
                                    @if(!$review->is_approved)
                                        <span class="badge bg-warning">Pending Approval</span>
                                    @else
                                        <span class="badge bg-success">Approved</span>
                                    @endif
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                @for($i = 1; $i <= 5; $i++)
                                    @if($i <= $review->rating)
                                        <i class="fas fa-star text-warning"></i>
                                    @else
                                        <i class="far fa-star text-warning"></i>
                                    @endif
                                @endfor
                                <span class="ms-2 text-muted">{{ $review->created_at->format('M d, Y \a\t g:i A') }}</span>
                            </div>
                            
                            <div class="mb-3">
                                <strong>Product:</strong> 
                                <a href="{{ route('products.show', $review->product->slug) }}" target="_blank" class="text-decoration-none">
                                    {{ $review->product->name }}
                                    <i class="fas fa-external-link-alt ms-1 small"></i>
                                </a>
                            </div>
                            
                            <div class="mb-0">
                                <strong>Review:</strong>
                                <p class="mt-2">{{ $review->comment }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Vendor Response Form -->
                    @if($review->is_approved)
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    @if($review->vendor_response)
                                        Update Your Response
                                    @else
                                        Write Your Response
                                    @endif
                                </h5>
                            </div>
                            <div class="card-body">
                                <form action="{{ $review->vendor_response ? route('vendor.reviews.update-response', $review) : route('vendor.reviews.respond', $review) }}" method="POST">
                                    @csrf
                                    @if($review->vendor_response)
                                        @method('PUT')
                                    @endif
                                    
                                    <div class="mb-3">
                                        <label for="vendor_response" class="form-label">Your Response <span class="text-danger">*</span></label>
                                        <textarea 
                                            class="form-control @error('vendor_response') is-invalid @enderror" 
                                            id="vendor_response" 
                                            name="vendor_response" 
                                            rows="4" 
                                            placeholder="Thank the customer and address their feedback professionally..."
                                            required
                                        >{{ old('vendor_response', $review->vendor_response) }}</textarea>
                                        @error('vendor_response')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="text-muted">Maximum 1000 characters. Be professional and helpful.</small>
                                    </div>
                                    
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            @if($review->vendor_response)
                                                <i class="fas fa-save me-1"></i>Update Response
                                            @else
                                                <i class="fas fa-reply me-1"></i>Post Response
                                            @endif
                                        </button>
                                        <a href="{{ route('vendor.reviews.index') }}" class="btn btn-outline-secondary">Cancel</a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    @else
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-3x text-warning mb-3"></i>
                                <h5 class="text-muted">Review Pending Approval</h5>
                                <p class="text-muted">This review is waiting for admin approval. You'll be able to respond once it's approved.</p>
                            </div>
                        </div>
                    @endif
                </div>

                <div class="col-md-4">
                    <!-- Response Guidelines -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Response Guidelines</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Thank the customer for their feedback
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Address specific concerns mentioned
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Offer solutions or assistance
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Keep it professional and friendly
                                </li>
                                <li class="mb-0">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Invite them to contact you directly
                                </li>
                            </ul>
                        </div>
                    </div>

                    @if($review->vendor_response)
                        <!-- Current Response -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">Current Response</h6>
                            </div>
                            <div class="card-body">
                                <p class="mb-2">{{ $review->vendor_response }}</p>
                                <small class="text-muted">Posted on {{ $review->vendor_response_date->format('M d, Y') }}</small>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
