<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Vendor;
use App\Models\Category;

class ProductController extends Controller
{
    public function index()
    {
        $products = Product::with(['vendor', 'category'])->paginate(15);
        return view('admin.products.index', compact('products'));
    }
    public function create()
    {
        $vendors = Vendor::all();
        $categories = Category::all();
        return view('admin.products.create', compact('vendors', 'categories'));
    }
    public function store(Request $request)
    {
        $data = $request->validate([
            'vendor_id' => 'required|exists:vendors,id',
            'category_id' => 'required|exists:categories,id',
            'name' => 'required',
            'slug' => 'required|unique:products',
            'description' => 'nullable',
            'price' => 'required|numeric',
            'discount_price' => 'nullable|numeric',
            'image_url' => 'nullable',
            'stock' => 'required|integer',
            'is_active' => 'boolean',
        ]);
        Product::create($data);
        return redirect()->route('admin.products.index')->with('success', 'Product created successfully.');
    }
    public function edit(Product $product)
    {
        $vendors = Vendor::all();
        $categories = Category::all();
        return view('admin.products.edit', compact('product', 'vendors', 'categories'));
    }
    public function update(Request $request, Product $product)
    {
        $data = $request->validate([
            'vendor_id' => 'required|exists:vendors,id',
            'category_id' => 'required|exists:categories,id',
            'name' => 'required',
            'slug' => 'required|unique:products,slug,'.$product->id,
            'description' => 'nullable',
            'price' => 'required|numeric',
            'discount_price' => 'nullable|numeric',
            'image_url' => 'nullable',
            'stock' => 'required|integer',
            'is_active' => 'boolean',
        ]);
        $product->update($data);
        return redirect()->route('admin.products.index')->with('success', 'Product updated successfully.');
    }
    public function destroy(Product $product)
    {
        $product->delete();
        return redirect()->route('admin.products.index')->with('success', 'Product deleted successfully.');
    }
}
