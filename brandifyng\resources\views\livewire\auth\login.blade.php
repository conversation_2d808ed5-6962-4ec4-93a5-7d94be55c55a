<div class="auth-form">
    <h2 class="text-center mb-4">{{ __('Log in to your account') }}</h2>
    <p class="text-center text-muted mb-4">{{ __('Enter your email and password below to log in') }}</p>

    <!-- Session Status -->
    @if (session('status'))
        <div class="alert alert-success text-center mb-4">
            {{ session('status') }}
        </div>
    @endif

    <form wire:submit="login" class="mb-4" wire:loading.class="opacity-50">
        <!-- Email Address -->
        <div class="form-group mb-3">
            <label for="email" class="form-label">{{ __('Email address') }}</label>
            <input wire:model="email" id="email" type="email"
                class="form-control @error('email') is-invalid @enderror" required autofocus autocomplete="email"
                placeholder="<EMAIL>">
            @error('email')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- Password -->
        <div class="form-group mb-3">
            <div class="d-flex justify-content-between align-items-center">
                <label for="password" class="form-label">{{ __('Password') }}</label>
                @if (Route::has('password.request'))
                    <a href="{{ route('password.request') }}" class="auth-link text-sm" wire:navigate>
                        {{ __('Forgot your password?') }}
                    </a>
                @endif
            </div>
            <input wire:model="password" id="password" type="password"
                class="form-control @error('password') is-invalid @enderror" required autocomplete="current-password"
                placeholder="{{ __('Password') }}">
            @error('password')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- Remember Me -->
        <div class="form-check mb-3">
            <input wire:model="remember" class="form-check-input" type="checkbox" id="remember">
            <label class="form-check-label" for="remember">
                {{ __('Remember me') }}
            </label>
        </div>

        <button type="submit" class="btn-primary w-100 mb-3" wire:loading.attr="disabled">
            <span wire:loading.remove>{{ __('Log in') }}</span>
            <span wire:loading>
                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                {{ __('Logging in...') }}
            </span>
        </button>
    </form>

    @if (Route::has('register'))
        <div class="text-center text-sm text-muted">
            {{ __('Don\'t have an account?') }}
            <a href="{{ route('register') }}" class="auth-link" wire:navigate>{{ __('Sign up') }}</a>
        </div>
    @endif
</div>
