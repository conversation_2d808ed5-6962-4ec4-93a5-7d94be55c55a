<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Review extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'product_id',
        'vendor_id',
        'rating',
        'comment',
        'is_approved',
        'vendor_response',
        'vendor_response_date',
    ];

    protected $casts = [
        'rating' => 'integer',
        'is_approved' => 'boolean',
        'vendor_response_date' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Check if vendor has responded to this review
     */
    public function hasVendorResponse(): bool
    {
        return !empty($this->vendor_response);
    }

    /**
     * Scope for approved reviews
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    /**
     * Scope for reviews with vendor responses
     */
    public function scopeWithVendorResponse($query)
    {
        return $query->whereNotNull('vendor_response');
    }
}
