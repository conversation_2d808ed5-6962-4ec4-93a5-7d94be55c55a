@extends('layouts.vendor')

@section('content')
    <div class="container-fluid">
        <div class="mb-4">
            <a href="{{ route('vendor.products.index') }}" class="text-decoration-none">
                <i class="fas fa-arrow-left me-1"></i> Back to Products
            </a>
        </div>

        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0 text-gray-800">Add New Product</h1>
        </div>

        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <form action="{{ route('vendor.products.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf

                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="name" class="form-label fw-bold">Product Name <span
                                        class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror"
                                    id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label fw-bold">Description <span
                                        class="text-danger">*</span></label>
                                <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description"
                                    rows="6" required>{{ old('description') }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="image" class="form-label fw-bold">Product Image</label>
                                <input type="file" class="form-control @error('image') is-invalid @enderror"
                                    id="image" name="image">
                                <small class="text-muted">Recommended size: 800x800px, max 2MB</small>
                                @error('image')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3 mt-4">
                                <div class="border rounded p-3 text-center" id="image-preview">
                                    <img src="https://via.placeholder.com/400x400?text=No+Image" id="preview"
                                        class="img-fluid rounded" alt="Product Image">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="category_id" class="form-label fw-bold">Category <span
                                        class="text-danger">*</span></label>
                                <select class="form-select @error('category_id') is-invalid @enderror" id="category_id"
                                    name="category_id" required>
                                    <option value="">Select Category</option>
                                    @foreach ($categories as $category)
                                        <option value="{{ $category->id }}"
                                            {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('category_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Brand</label>
                                <input type="text" class="form-control" value="{{ auth()->user()->vendor->shop_name }}"
                                    readonly>
                                <small class="text-muted">Your vendor shop represents your brand in this multi-vendor
                                    system.</small>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="sku" class="form-label fw-bold">SKU</label>
                                <input type="text" class="form-control @error('sku') is-invalid @enderror" id="sku"
                                    name="sku" value="{{ old('sku') }}">
                                @error('sku')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="price" class="form-label fw-bold">Price (₦) <span
                                        class="text-danger">*</span></label>
                                <input type="number" step="0.01"
                                    class="form-control @error('price') is-invalid @enderror" id="price" name="price"
                                    value="{{ old('price') }}" required>
                                @error('price')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="discount_price" class="form-label fw-bold">Discount Price (₦)</label>
                                <input type="number" step="0.01"
                                    class="form-control @error('discount_price') is-invalid @enderror" id="discount_price"
                                    name="discount_price" value="{{ old('discount_price') }}">
                                @error('discount_price')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="stock_quantity" class="form-label fw-bold">Stock Quantity <span
                                        class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('stock_quantity') is-invalid @enderror"
                                    id="stock_quantity" name="stock_quantity" value="{{ old('stock_quantity', 1) }}"
                                    required>
                                @error('stock_quantity')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Status</label>
                                <div class="form-check form-switch mt-2">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                        checked>
                                    <label class="form-check-label" for="is_active">Active</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Shipping Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="fw-bold mb-3">Shipping Information</h5>
                            <p class="text-muted small mb-3">Required for accurate shipping calculations and delivery
                                management.</p>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="weight" class="form-label fw-bold">Weight (kg)</label>
                                <input type="number" step="0.01"
                                    class="form-control @error('weight') is-invalid @enderror" id="weight"
                                    name="weight" value="{{ old('weight', 1.0) }}">
                                @error('weight')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="length" class="form-label fw-bold">Length (cm)</label>
                                <input type="number" step="0.1"
                                    class="form-control @error('length') is-invalid @enderror" id="length"
                                    name="length" value="{{ old('length', 20) }}">
                                @error('length')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="width" class="form-label fw-bold">Width (cm)</label>
                                <input type="number" step="0.1"
                                    class="form-control @error('width') is-invalid @enderror" id="width"
                                    name="width" value="{{ old('width', 15) }}">
                                @error('width')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="height" class="form-label fw-bold">Height (cm)</label>
                                <input type="number" step="0.1"
                                    class="form-control @error('height') is-invalid @enderror" id="height"
                                    name="height" value="{{ old('height', 10) }}">
                                @error('height')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="requires_platform_delivery"
                                    name="requires_platform_delivery" value="1"
                                    {{ old('requires_platform_delivery') ? 'checked' : '' }}>
                                <label class="form-check-label" for="requires_platform_delivery">
                                    <strong>Requires Platform Delivery</strong>
                                    <small class="text-muted d-block">Check this if the product requires platform-managed
                                        delivery (applies to vendors in Lagos, Abuja, Ibadan, Akure)</small>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-12">
                            <label class="form-label fw-bold">Specifications/Attributes</label>
                            <div class="table-responsive mb-2">
                                <table class="table table-bordered" id="attributes-table">
                                    <thead class="bg-light">
                                        <tr>
                                            <th>Attribute Name</th>
                                            <th>Value</th>
                                            <th style="width: 50px;"></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <input type="text" class="form-control" name="attributes[0][name]"
                                                    placeholder="e.g. Color, Size, Material">
                                            </td>
                                            <td>
                                                <input type="text" class="form-control" name="attributes[0][value]"
                                                    placeholder="e.g. Red, XL, Cotton">
                                            </td>
                                            <td>
                                                <button type="button"
                                                    class="btn btn-sm btn-outline-danger remove-attribute"><i
                                                        class="fas fa-times"></i></button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-dark" id="add-attribute">
                                <i class="fas fa-plus me-1"></i> Add Attribute
                            </button>
                        </div>
                    </div>

                    {{-- Product Variants Section --}}
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="mb-3 fw-bold">Product Variants</h5>
                            <p class="text-muted small">
                                Add color and size options for this product. Each variant can have its own SKU, price
                                adjustment, stock level, and image.
                                If no variants are added, the main product price and stock will be used.
                            </p>
                            <div id="variants-container">
                                {{-- Variant rows will be appended here by JavaScript --}}
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-primary mt-2" id="add-variant-btn">
                                <i class="fas fa-plus me-1"></i> Add Variant
                            </button>
                        </div>
                    </div>

                    {{-- Hidden Template for a Single Variant Row --}}
                    <template id="variant-row-template">
                        <div class="variant-row border rounded p-3 mb-3 bg-light">
                            <input type="hidden" name="variants[__INDEX__][id]" value=""> {{-- For existing variants in edit form --}}
                            <div class="row align-items-center">
                                <div class="col-md-3 mb-2">
                                    <label class="form-label small">Color</label>
                                    <select name="variants[__INDEX__][color_id]"
                                        class="form-select form-select-sm variant-color">
                                        <option value="">None</option>
                                        @foreach ($colors as $color)
                                            <option value="{{ $color->id }}">{{ $color->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <label class="form-label small">Size</label>
                                    <select name="variants[__INDEX__][size_id]"
                                        class="form-select form-select-sm variant-size">
                                        <option value="">None</option>
                                        @foreach ($sizes as $size)
                                            <option value="{{ $size->id }}">{{ $size->name }}
                                                {{ $size->code ? '(' . $size->code . ')' : '' }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-2 mb-2">
                                    <label class="form-label small">SKU</label>
                                    <input type="text" name="variants[__INDEX__][sku]"
                                        class="form-control form-control-sm variant-sku" placeholder="Variant SKU">
                                </div>
                                <div class="col-md-2 mb-2">
                                    <label class="form-label small">Price Adj. ($)</label>
                                    <input type="number" step="0.01" name="variants[__INDEX__][price_adjustment]"
                                        class="form-control form-control-sm variant-price-adj" placeholder="e.g. -2.00">
                                </div>
                                <div class="col-md-2 mb-2">
                                    <label class="form-label small">Stock <span class="text-danger">*</span></label>
                                    <input type="number" name="variants[__INDEX__][stock_quantity]"
                                        class="form-control form-control-sm variant-stock" placeholder="Stock"
                                        value="0" required>
                                </div>
                            </div>
                            <div class="row align-items-center mt-2">
                                <div class="col-md-10 mb-2">
                                    <label class="form-label small">Variant Image</label>
                                    <input type="file" name="variants[__INDEX__][image]"
                                        class="form-control form-control-sm variant-image">
                                </div>
                                <div class="col-md-2 d-flex align-items-end mb-2">
                                    <button type="button" class="btn btn-sm btn-outline-danger remove-variant-btn w-100">
                                        <i class="fas fa-times"></i> Remove
                                    </button>
                                </div>
                            </div>
                            <hr class="mt-3 mb-0">
                        </div>
                    </template>

                    <div class="d-flex justify-content-end mt-4">
                        <a href="{{ route('vendor.products.index') }}" class="btn btn-outline-secondary me-2">Cancel</a>
                        <button type="submit" class="btn btn-dark">Save Product</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Image preview
                const imageInput = document.getElementById('image');
                const imagePreview = document.getElementById('preview');

                if (imageInput && imagePreview) { // Added null check for safety
                    imageInput.addEventListener('change', function() {
                        if (this.files && this.files[0]) {
                            const reader = new FileReader();

                            reader.onload = function(e) {
                                imagePreview.src = e.target.result;
                            }

                            reader.readAsDataURL(this.files[0]);
                        } else {
                            imagePreview.src =
                                'https://via.placeholder.com/400x400?text=No+Image'; // Reset if no file selected
                        }
                    });
                }

                // Add Attribute (existing script, ensure it's compatible or adjust if needed)
                const attributesTableBody = document.querySelector('#attributes-table tbody');
                const addAttributeButton = document.getElementById('add-attribute');
                let attributeCounter = 1; // Assuming existing attributes start at 0

                if (addAttributeButton && attributesTableBody) { // Added null check
                    addAttributeButton.addEventListener('click', function() {
                        const newRow = `
                    <tr>
                        <td>
                            <input type="text" class="form-control" name="attributes[${attributeCounter}][name]" placeholder="e.g. Color, Size, Material">
                        </td>
                        <td>
                            <input type="text" class="form-control" name="attributes[${attributeCounter}][value]" placeholder="e.g. Red, XL, Cotton">
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-outline-danger remove-attribute"><i class="fas fa-times"></i></button>
                        </td>
                    </tr>
                `;
                        attributesTableBody.insertAdjacentHTML('beforeend', newRow);
                        attributeCounter++;
                    });

                    attributesTableBody.addEventListener('click', function(e) {
                        if (e.target.classList.contains('remove-attribute') || e.target.closest(
                                '.remove-attribute')) {
                            e.target.closest('tr').remove();
                        }
                    });
                }

                // Product Variant Management
                const variantsContainer = document.getElementById('variants-container');
                const addVariantBtn = document.getElementById('add-variant-btn');
                const variantRowTemplate = document.getElementById('variant-row-template');
                let variantIndex = 0; // Counter for unique input names

                if (addVariantBtn && variantsContainer && variantRowTemplate) {
                    addVariantBtn.addEventListener('click', function() {
                        // Clone the template content
                        const templateNode = variantRowTemplate.content.cloneNode(true);
                        // Get the actual variant row element from the cloned template
                        const newVariantRow = templateNode.firstElementChild;

                        // Update input names and IDs with the current index
                        newVariantRow.querySelectorAll('[name]').forEach(input => {
                            input.name = input.name.replace('__INDEX__', variantIndex);
                            if (input.id) {
                                input.id = input.id.replace('__INDEX__', variantIndex);
                            }
                        });
                        newVariantRow.querySelectorAll('[for]').forEach(label => {
                            label.htmlFor = label.htmlFor.replace('__INDEX__', variantIndex);
                        });

                        variantsContainer.appendChild(newVariantRow);
                        variantIndex++;
                    });

                    variantsContainer.addEventListener('click', function(event) {
                        let targetElement = event.target;
                        // Traverse up if the click was on the icon inside the button
                        if (targetElement.tagName === 'I' && targetElement.parentElement.classList.contains(
                                'remove-variant-btn')) {
                            targetElement = targetElement.parentElement;
                        }

                        if (targetElement.classList.contains('remove-variant-btn')) {
                            const rowToRemove = targetElement.closest('.variant-row');
                            if (rowToRemove) {
                                rowToRemove.remove();
                                // Note: We are not re-indexing existing variants on removal to keep things simple.
                                // The backend will handle the submitted list as is.
                            }
                        }
                    });
                }
            });
        </script>
    @endpush
@endsection
