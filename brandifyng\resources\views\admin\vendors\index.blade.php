@extends('layouts.admin')
@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="fw-bold">Vendors</h2>
    </div>
    <div class="card">
        <div class="card-body p-0">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Shop Name</th>
                        <th>User</th>
                        <th>Status</th>
                        <th>Featured</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($vendors as $vendor)
                    <tr>
                        <td>{{ $vendor->shop_name }}</td>
                        <td>{{ $vendor->user->name ?? '' }}<br><small>{{ $vendor->user->email ?? '' }}</small></td>
                        <td>
                            @if($vendor->is_approved)
                                <span class="badge bg-success">Approved</span>
                            @else
                                <span class="badge bg-warning text-dark">Pending</span>
                            @endif
                        </td>
                        <td>
                            @if($vendor->is_featured)
                                <span class="badge bg-primary">Featured</span>
                            @else
                                <span class="badge bg-secondary">Not Featured</span>
                            @endif
                        </td>
                        <td>
                            <a href="{{ route('admin.vendors.show', $vendor) }}" class="btn btn-sm btn-dark me-2">View</a>
                            <a href="{{ route('admin.vendors.edit', $vendor) }}" class="btn btn-sm btn-outline-dark me-2">Edit</a>
                            <form action="{{ route('admin.vendors.destroy', $vendor) }}" method="POST" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-sm btn-outline-danger">Delete</button>
                            </form>
                            @if(!$vendor->is_approved)
                                <form action="{{ route('admin.vendors.approve', $vendor->id) }}" method="POST" class="d-inline ms-2">
                                    @csrf
                                    <button type="submit" class="btn btn-sm btn-success">Approve</button>
                                </form>
                            @else
                                <form action="{{ route('admin.vendors.reject', $vendor->id) }}" method="POST" class="d-inline ms-2">
                                    @csrf
                                    <button type="submit" class="btn btn-sm btn-warning">Reject</button>
                                </form>
                            @endif
                            
                            <form action="{{ route('admin.vendors.toggle-featured', $vendor->id) }}" method="POST" class="d-inline ms-2">
                                @csrf
                                <button type="submit" class="btn btn-sm {{ $vendor->is_featured ? 'btn-outline-primary' : 'btn-primary' }}">
                                    {{ $vendor->is_featured ? 'Unfeature' : 'Feature' }}
                                </button>
                            </form>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
    <div class="mt-3">
        {{ $vendors->links() }}
    </div>
</div>
@endsection
