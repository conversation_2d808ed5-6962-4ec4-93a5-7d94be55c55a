<?php

namespace App\Services;

class DeliveryZoneService
{
    /**
     * Determine delivery zone type based on state
     *
     * @param string $state
     * @return string
     */
    public function determineDeliveryZoneType(string $state): string
    {
        $platformDeliveryStates = config('ecommerce.delivery_zones.platform_delivery_zone.states', []);
        
        return in_array($state, $platformDeliveryStates) 
            ? 'platform_delivery_zone' 
            : 'other_states_zone';
    }

    /**
     * Get subscription fee for a delivery zone
     *
     * @param string $zoneType
     * @return int
     */
    public function getSubscriptionFee(string $zoneType): int
    {
        return config("ecommerce.delivery_zones.{$zoneType}.subscription_fee", 0);
    }

    /**
     * Get delivery policy for a zone
     *
     * @param string $zoneType
     * @return array
     */
    public function getDeliveryPolicy(string $zoneType): array
    {
        $config = config("ecommerce.delivery_zones.{$zoneType}", []);
        
        if ($zoneType === 'platform_delivery_zone') {
            return [
                'type' => 'platform_delivery',
                'description' => $config['description'] ?? 'Platform handles delivery',
                'delivery_days' => $config['delivery_days'] ?? [],
                'fee' => $config['subscription_fee'] ?? 0
            ];
        }
        
        return [
            'type' => 'vendor_delivery',
            'description' => $config['description'] ?? 'Vendor handles delivery',
            'fee' => $config['subscription_fee'] ?? 0
        ];
    }

    /**
     * Check if state is in platform delivery zone
     *
     * @param string $state
     * @return bool
     */
    public function isPlatformDeliveryZone(string $state): bool
    {
        return $this->determineDeliveryZoneType($state) === 'platform_delivery_zone';
    }

    /**
     * Get all platform delivery states
     *
     * @return array
     */
    public function getPlatformDeliveryStates(): array
    {
        return config('ecommerce.delivery_zones.platform_delivery_zone.states', []);
    }

    /**
     * Get delivery days for platform delivery zone
     *
     * @return array
     */
    public function getPlatformDeliveryDays(): array
    {
        return config('ecommerce.delivery_zones.platform_delivery_zone.delivery_days', []);
    }

    /**
     * Get subscription plan details for a zone
     *
     * @param string $zoneType
     * @return array
     */
    public function getSubscriptionPlan(string $zoneType): array
    {
        return config("ecommerce.subscription_plans.{$zoneType}", []);
    }

    /**
     * Get all Nigerian states
     *
     * @return array
     */
    public function getAllNigerianStates(): array
    {
        return config('ecommerce.nigerian_market.states', []);
    }

    /**
     * Validate Nigerian phone number
     *
     * @param string $phone
     * @return bool
     */
    public function validateNigerianPhone(string $phone): bool
    {
        $pattern = config('ecommerce.nigerian_market.phone_validation_pattern');
        return preg_match($pattern, $phone);
    }

    /**
     * Format Nigerian phone number
     *
     * @param string $phone
     * @return string
     */
    public function formatNigerianPhone(string $phone): string
    {
        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Handle different formats
        if (strlen($phone) === 11 && substr($phone, 0, 1) === '0') {
            // Convert 0XXXXXXXXXX to +234XXXXXXXXXX
            return '+234' . substr($phone, 1);
        } elseif (strlen($phone) === 10) {
            // Convert XXXXXXXXXX to +234XXXXXXXXXX
            return '+234' . $phone;
        } elseif (strlen($phone) === 13 && substr($phone, 0, 3) === '234') {
            // Convert 234XXXXXXXXXX to +234XXXXXXXXXX
            return '+' . $phone;
        } elseif (strlen($phone) === 14 && substr($phone, 0, 4) === '+234') {
            // Already in correct format
            return $phone;
        }
        
        // Return as-is if format is not recognized
        return $phone;
    }

    /**
     * Get delivery zone information for display
     *
     * @param string $state
     * @return array
     */
    public function getDeliveryZoneInfo(string $state): array
    {
        $zoneType = $this->determineDeliveryZoneType($state);
        $policy = $this->getDeliveryPolicy($zoneType);
        $plan = $this->getSubscriptionPlan($zoneType);
        
        return [
            'zone_type' => $zoneType,
            'state' => $state,
            'is_platform_delivery' => $zoneType === 'platform_delivery_zone',
            'subscription_fee' => $this->getSubscriptionFee($zoneType),
            'delivery_policy' => $policy,
            'subscription_plan' => $plan,
            'currency' => config('ecommerce.commission.currency', 'NGN'),
            'currency_symbol' => config('ecommerce.commission.currency_symbol', '₦')
        ];
    }
}
