<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->string('product_name')->nullable()->after('product_id');
            $table->decimal('unit_price', 10, 2)->nullable()->after('price');
            $table->decimal('subtotal', 10, 2)->nullable()->after('unit_price');
            $table->string('status')->default('pending')->after('subtotal');
            $table->foreignId('vendor_id')->nullable()->after('order_id')->constrained('vendors');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->dropColumn(['product_name', 'unit_price', 'subtotal', 'status']);
            $table->dropForeign(['vendor_id']);
            $table->dropColumn('vendor_id');
        });
    }
};
